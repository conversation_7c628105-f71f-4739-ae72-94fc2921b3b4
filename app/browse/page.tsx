"use client"

import { useState, use<PERSON>emo, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { Filter, Grid3X3, List } from "lucide-react"
import { LoadingState } from "@/components/ui/loading-state"
import { ErrorState } from "@/components/ui/error-state"
import { EmptyState } from "@/components/ui/empty-state"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { PresentationCard } from "@/components/presentation-card"
import { fetchAllTemplates, fetchAllCategories, fetchTemplatesByCategory } from "@/lib/api"
import { Template } from "@/lib/templates-data"

const categories = [
  "All",
  "Business",
  "Marketing",
  "Creative",
  "Corporate",
  "Finance",
  "Education",
  "Technology",
  "Sales",
  "Healthcare",
]
const formats = ["All Formats", "PowerPoint", "Google Slides", "Keynote", "Canva"]
const templateTypes = ["All Templates", "Free Only", "Premium Only"]
const sortOptions = [
  { value: "popular", label: "Most Popular" },
  { value: "newest", label: "Newest First" },
  { value: "oldest", label: "Oldest First" },
  { value: "rating", label: "Highest Rated" },
  { value: "downloads", label: "Most Downloaded" },
]

export default function BrowsePage() {
  const searchParams = useSearchParams()

  // Initialize filters from URL parameters
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get("category") || "All")
  const [selectedFormat, setSelectedFormat] = useState("All Formats")
  const [selectedType, setSelectedType] = useState("All Templates")
  const [sortBy, setSortBy] = useState("popular")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [minRating, setMinRating] = useState(0)
  
  // API data state
  const [allPresentations, setAllPresentations] = useState<Template[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Fetch templates from API
  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true)
        setError(null)
        
        // Fetch all templates
        const templates = await fetchAllTemplates()
        setAllPresentations(templates)
      } catch (err) {
        console.error('Error fetching templates:', err)
        setError('Failed to load templates. Please try again later.')
      } finally {
        setLoading(false)
      }
    }
    
    fetchData()
  }, [])

  // Get all unique tags
  const allTags = useMemo(() => {
    const tags = new Set<string>()
    allPresentations.forEach((presentation: Template) => {
      presentation.tags.forEach((tag: string) => tags.add(tag))
    })
    return Array.from(tags).sort()
  }, [allPresentations])

  // Filter and sort presentations
  const filteredPresentations = useMemo(() => {
    const filtered = allPresentations.filter((presentation: Template) => {
      // Search query filter
      if (
        searchQuery &&
        !presentation.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !presentation.tags.some((tag: string) => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      ) {
        return false
      }

      // Category filter
      if (selectedCategory !== "All" && presentation.category !== selectedCategory) {
        return false
      }

      // Format filter
      if (selectedFormat !== "All Formats" && !presentation.formats.includes(selectedFormat)) {
        return false
      }

      // Template type filter
      if (selectedType === "Free Only" && presentation.isPremium) {
        return false
      }
      if (selectedType === "Premium Only" && !presentation.isPremium) {
        return false
      }

      // Tags filter
      if (selectedTags.length > 0 && !selectedTags.some((tag) => presentation.tags.includes(tag))) {
        return false
      }

      // Rating filter
      if (presentation.rating < minRating) {
        return false
      }

      return true
    })

    // Sort presentations
    filtered.sort((a: Template, b: Template) => {
      switch (sortBy) {
        case "newest":
          return new Date(b.dateAdded).getTime() - new Date(a.dateAdded).getTime()
        case "oldest":
          return new Date(a.dateAdded).getTime() - new Date(b.dateAdded).getTime()
        case "rating":
          return b.rating - a.rating
        case "downloads":
          return (
            Number.parseInt(b.downloads.replace("k", "000").replace(".", "")) -
            Number.parseInt(a.downloads.replace("k", "000").replace(".", ""))
          )
        case "popular":
        default:
          return (
            Number.parseInt(b.downloads.replace("k", "000").replace(".", "")) -
            Number.parseInt(a.downloads.replace("k", "000").replace(".", ""))
          )
      }
    })

    return filtered
  }, [allPresentations, searchQuery, selectedCategory, selectedFormat, selectedType, sortBy, selectedTags, minRating])

  const handleTagToggle = (tag: string) => {
    setSelectedTags((prev) => (prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag]))
  }

  const clearFilters = () => {
    setSearchQuery("")
    setSelectedCategory("All")
    setSelectedFormat("All Formats")
    setSelectedType("All Templates")
    setSortBy("popular")
    setSelectedTags([])
    setMinRating(0)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Page Header */}
      <section className="bg-white border-b border-gray-200 py-8 px-4">
        <div className="max-w-[1400px] mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Browse Templates</h1>
          <p className="text-gray-600">
            Discover our complete collection of {allPresentations.length} professional presentation templates
          </p>
        </div>
      </section>

      <div className="max-w-[1400px] mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Filters */}
          <div className="lg:w-80 space-y-6">
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-lg">Filters</h3>
                <Button variant="ghost" size="sm" onClick={clearFilters}>
                  Clear All
                </Button>
              </div>

              {/* Search */}
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Search</label>
                  <div className="relative">
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="11" cy="11" r="8"></circle>
                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                      </svg>
                    </div>
                    <Input
                      placeholder="Search templates..."
                      className="pl-10 w-full"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>

                {/* Template Type */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Template Type</label>
                  <Select value={selectedType} onValueChange={setSelectedType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {templateTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Category */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Category</label>
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Format */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Format</label>
                  <Select value={selectedFormat} onValueChange={setSelectedFormat}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {formats.map((format) => (
                        <SelectItem key={format} value={format}>
                          {format}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Minimum Rating */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Minimum Rating</label>
                  <Select value={minRating.toString()} onValueChange={(value) => setMinRating(Number(value))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Any Rating</SelectItem>
                      <SelectItem value="4">4+ Stars</SelectItem>
                      <SelectItem value="4.5">4.5+ Stars</SelectItem>
                      <SelectItem value="4.8">4.8+ Stars</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Tags */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">Tags</label>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {allTags.map((tag) => (
                      <div key={tag} className="flex items-center space-x-2">
                        <Checkbox
                          id={tag}
                          checked={selectedTags.includes(tag)}
                          onCheckedChange={() => handleTagToggle(tag)}
                        />
                        <label htmlFor={tag} className="text-sm text-gray-700 capitalize cursor-pointer">
                          {tag}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Controls Bar */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4">
              <div className="flex items-center gap-4">
                {!loading && <span className="text-sm text-gray-600">{filteredPresentations.length} templates found</span>}
                {!loading && (searchQuery ||
                  selectedCategory !== "All" ||
                  selectedFormat !== "All Formats" ||
                  selectedType !== "All Templates" ||
                  selectedTags.length > 0 ||
                  minRating > 0) && (
                  <Button variant="outline" size="sm" onClick={clearFilters} disabled={loading}>
                    <Filter className="h-4 w-4 mr-2" />
                    Clear Filters
                  </Button>
                )}
              </div>

              <div className="flex items-center gap-4">
                {/* Sort */}
                <Select value={sortBy} onValueChange={setSortBy} disabled={loading}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {sortOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* View Mode */}
                <div className="flex border rounded-lg">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-r-none"
                    disabled={loading}
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="rounded-l-none"
                    disabled={loading}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Results */}
            {loading ? (
              <LoadingState message="Loading templates..." size="medium" />
            ) : error ? (
              <ErrorState 
                message={error} 
                retryAction={() => window.location.reload()} 
                showRetry={true}
              />
            ) : filteredPresentations.length === 0 ? (
              <EmptyState
                title="No templates found"
                message="Try adjusting your filters or search terms"
                actionLabel="Clear All Filters"
                actionFn={clearFilters}
              />
            ) : (
              <div
                className={viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 px-4" : "space-y-4"}
              >
                {filteredPresentations.map((presentation: Template) => (
                  <PresentationCard key={presentation.id} presentation={presentation} viewMode={viewMode} />
                ))}
              </div>
            )}

            {/* Load More */}
            {!loading && filteredPresentations.length > 0 && (
              <div className="text-center mt-12">
                <Button variant="outline" size="lg" className="px-8 bg-transparent" disabled={loading}>
                  Load More Templates
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
