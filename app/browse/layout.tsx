import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"

export const metadata: Metadata = {
  title: "Browse Templates - TemplateHub",
  description:
    "Browse our complete collection of professional presentation templates for PowerPoint, Google Slides, Keynote, and Canva. Filter by category, price, rating, and more.",
  keywords: "browse templates, presentation templates, PowerPoint templates, Google Slides templates, filter templates",
}

export default function BrowseLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
