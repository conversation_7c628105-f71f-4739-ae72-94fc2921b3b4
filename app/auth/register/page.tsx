"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { AuthForm } from "@/components/auth/auth-form"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { useAuth } from "@/hooks/use-auth"

export default function RegisterPage() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const router = useRouter()
  const { register } = useAuth()

  const handleRegister = async (data: { email: string; password: string; name: string }) => {
    setLoading(true)
    setError(null)

    try {
      await register(data.email, data.password, data.name)
      setSuccess(true)
      // Redirect to email verification page
      setTimeout(() => {
        router.push("/auth/verify-email?email=" + encodeURIComponent(data.email))
      }, 2000)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Registration failed")
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />

        <div className="flex items-center justify-center py-16 px-4">
          <div className="w-full max-w-md text-center">
            <div className="bg-white p-8 rounded-lg shadow-sm border">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Account Created!</h2>
              <p className="text-gray-600 mb-4">
                We've sent a verification email to your inbox. Please check your email and click the verification link
                to activate your account.
              </p>
              <p className="text-sm text-gray-500">Redirecting to verification page...</p>
            </div>
          </div>
        </div>

        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="flex items-center justify-center py-16 px-4">
        <div className="w-full max-w-md">
          <AuthForm
            type="register"
            onSubmit={handleRegister}
            loading={loading}
            error={error}
            title="Create Account"
            description="Join TemplateHub to access premium templates"
            submitText="Create Account"
            footerText="Already have an account?"
            footerLink={{ text: "Sign in", href: "/auth/login" }}
          />
        </div>
      </div>

      <Footer />
    </div>
  )
}
