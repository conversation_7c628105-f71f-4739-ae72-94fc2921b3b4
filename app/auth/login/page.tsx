"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { AuthForm } from "@/components/auth/auth-form"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { useAuth } from "@/hooks/use-auth"

export default function LoginPage() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const { login } = useAuth()

  const handleLogin = async (data: { email: string; password: string }) => {
    setLoading(true)
    setError(null)

    try {
      await login(data.email, data.password)
      // Redirect to dashboard or previous page
      const returnUrl = new URLSearchParams(window.location.search).get("returnUrl")
      router.push(returnUrl || "/dashboard")
    } catch (err) {
      setError(err instanceof Error ? err.message : "<PERSON><PERSON> failed")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="flex items-center justify-center py-16 px-4">
        <div className="w-full max-w-md">
          <AuthForm
            type="login"
            onSubmit={handleLogin}
            loading={loading}
            error={error}
            title="Welcome Back"
            description="Sign in to your account to continue"
            submitText="Sign In"
            footerText="Don't have an account?"
            footerLink={{ text: "Sign up", href: "/auth/register" }}
          />

          {/* Demo credentials */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="text-sm font-medium text-blue-900 mb-2">Demo Credentials</h3>
            <p className="text-sm text-blue-700">
              Email: <EMAIL>
              <br />
              Password: password
            </p>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
