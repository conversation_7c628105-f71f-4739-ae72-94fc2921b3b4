"use client"

import { useState } from "react"
import { AuthForm } from "@/components/auth/auth-form"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { authAPI } from "@/lib/auth"

export default function ForgotPasswordPage() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const handleForgotPassword = async (data: { email: string }) => {
    setLoading(true)
    setError(null)

    try {
      await authAPI.forgotPassword(data.email)
      setSuccess(true)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to send reset email")
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />

        <div className="flex items-center justify-center py-16 px-4">
          <div className="w-full max-w-md text-center">
            <div className="bg-white p-8 rounded-lg shadow-sm border">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Check Your Email</h2>
              <p className="text-gray-600 mb-6">
                We've sent a password reset link to your email address. Please check your inbox and follow the
                instructions to reset your password.
              </p>
              <p className="text-sm text-gray-500">
                Didn't receive the email? Check your spam folder or{" "}
                <button onClick={() => setSuccess(false)} className="text-blue-600 hover:text-blue-700 underline">
                  try again
                </button>
              </p>
            </div>
          </div>
        </div>

        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="flex items-center justify-center py-16 px-4">
        <div className="w-full max-w-md">
          <AuthForm
            type="forgot-password"
            onSubmit={handleForgotPassword}
            loading={loading}
            error={error}
            title="Forgot Password"
            description="Enter your email address and we'll send you a link to reset your password"
            submitText="Send Reset Link"
            footerText="Remember your password?"
            footerLink={{ text: "Sign in", href: "/auth/login" }}
          />
        </div>
      </div>

      <Footer />
    </div>
  )
}
