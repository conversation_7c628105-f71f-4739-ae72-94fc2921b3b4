"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { Loader2, Mail, CheckCircle, XCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { authAPI } from "@/lib/auth"

export default function VerifyEmailPage() {
  const [status, setStatus] = useState<"loading" | "success" | "error" | "pending">("pending")
  const [error, setError] = useState<string | null>(null)
  const [email, setEmail] = useState<string | null>(null)

  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const token = searchParams.get("token")
    const emailParam = searchParams.get("email")

    if (emailParam) {
      setEmail(decodeURIComponent(emailParam))
    }

    if (token) {
      // Verify the email token
      setStatus("loading")
      authAPI
        .verifyEmail(token)
        .then(() => {
          setStatus("success")
          setTimeout(() => {
            router.push("/auth/login")
          }, 3000)
        })
        .catch((err) => {
          setStatus("error")
          setError(err instanceof Error ? err.message : "Verification failed")
        })
    }
  }, [searchParams, router])

  const handleResendVerification = async () => {
    if (!email) return

    try {
      // In a real app, you'd have a resend verification endpoint
      await authAPI.forgotPassword(email) // Using forgot password as placeholder
      alert("Verification email sent!")
    } catch (err) {
      alert("Failed to resend verification email")
    }
  }

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />

        <div className="flex items-center justify-center py-16 px-4">
          <Card className="w-full max-w-md mx-auto text-center">
            <CardContent className="pt-6">
              <Loader2 className="w-12 h-12 animate-spin text-blue-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Verifying Email</h2>
              <p className="text-gray-600">Please wait while we verify your email address...</p>
            </CardContent>
          </Card>
        </div>

        <Footer />
      </div>
    )
  }

  if (status === "success") {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />

        <div className="flex items-center justify-center py-16 px-4">
          <Card className="w-full max-w-md mx-auto text-center">
            <CardContent className="pt-6">
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Email Verified!</h2>
              <p className="text-gray-600 mb-6">
                Your email has been successfully verified. You can now access all features of your account.
              </p>
              <p className="text-sm text-gray-500">Redirecting to login page...</p>
            </CardContent>
          </Card>
        </div>

        <Footer />
      </div>
    )
  }

  if (status === "error") {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />

        <div className="flex items-center justify-center py-16 px-4">
          <Card className="w-full max-w-md mx-auto text-center">
            <CardContent className="pt-6">
              <XCircle className="w-16 h-16 text-red-600 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Verification Failed</h2>
              <p className="text-gray-600 mb-6">{error || "The verification link is invalid or has expired."}</p>
              <div className="space-y-3">
                {email && (
                  <Button onClick={handleResendVerification} variant="outline" className="w-full bg-transparent">
                    Resend Verification Email
                  </Button>
                )}
                <Link href="/auth/login">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700">Go to Login</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>

        <Footer />
      </div>
    )
  }

  // Pending state - no token, just email
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="flex items-center justify-center py-16 px-4">
        <Card className="w-full max-w-md mx-auto text-center">
          <CardHeader>
            <CardTitle className="text-2xl font-bold">Verify Your Email</CardTitle>
            <CardDescription>
              We've sent a verification link to {email ? <strong>{email}</strong> : "your email address"}
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            <Mail className="w-16 h-16 text-blue-600 mx-auto" />

            <div className="space-y-4">
              <p className="text-gray-600">
                Please check your email and click the verification link to activate your account.
              </p>

              <div className="space-y-3">
                {email && (
                  <Button onClick={handleResendVerification} variant="outline" className="w-full bg-transparent">
                    Resend Verification Email
                  </Button>
                )}

                <Link href="/auth/login">
                  <Button variant="ghost" className="w-full">
                    Back to Login
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Footer />
    </div>
  )
}
