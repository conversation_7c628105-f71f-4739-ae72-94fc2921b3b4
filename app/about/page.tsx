import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Users, Target, Award, Globe, Heart, Zap, Shield, Lightbulb } from "lucide-react"
import Link from "next/link"

const teamMembers = [
  {
    name: "<PERSON>",
    role: "Founder & CEO",
    bio: "Former design director at top agencies with 12+ years creating presentations for Fortune 500 companies.",
    image: "/placeholder.svg?height=300&width=300&text=<PERSON>",
    linkedin: "#",
    twitter: "#",
  },
  {
    name: "<PERSON>",
    role: "Head of Design",
    bio: "Award-winning designer specializing in corporate presentations and visual storytelling.",
    image: "/placeholder.svg?height=300&width=300&text=<PERSON>",
    linkedin: "#",
    twitter: "#",
  },
  {
    name: "<PERSON>",
    role: "Product Manager",
    bio: "Product strategist focused on user experience and building tools that designers love to use.",
    image: "/placeholder.svg?height=300&width=300&text=<PERSON>+<PERSON>",
    linkedin: "#",
    twitter: "#",
  },
  {
    name: "<PERSON>",
    role: "Lead <PERSON>eloper",
    bio: "Full-stack engineer passionate about creating seamless digital experiences and scalable platforms.",
    image: "/placeholder.svg?height=300&width=300&text=David+Kim",
    linkedin: "#",
    twitter: "#",
  },
]

const values = [
  {
    icon: Heart,
    title: "User-Centric Design",
    description: "Every template is crafted with the end user in mind, ensuring professional results for everyone.",
  },
  {
    icon: Zap,
    title: "Innovation",
    description: "We constantly push the boundaries of presentation design with fresh, modern templates.",
  },
  {
    icon: Shield,
    title: "Quality Assurance",
    description: "Rigorous testing and review process ensures every template meets our high standards.",
  },
  {
    icon: Globe,
    title: "Accessibility",
    description: "Making professional design accessible to businesses and individuals worldwide.",
  },
]

const milestones = [
  {
    year: "2020",
    title: "Company Founded",
    description: "Started with a vision to democratize professional presentation design.",
  },
  {
    year: "2021",
    title: "First 1,000 Users",
    description: "Reached our first milestone with templates downloaded across 50 countries.",
  },
  {
    year: "2022",
    title: "Premium Launch",
    description: "Introduced premium subscriptions and expanded our template library to 500+ designs.",
  },
  {
    year: "2023",
    title: "100K Downloads",
    description: "Celebrated 100,000 template downloads and launched our mobile-friendly platform.",
  },
  {
    year: "2024",
    title: "Global Expansion",
    description: "Serving over 50,000 users worldwide with 1,000+ templates across all major platforms.",
  },
]

const stats = [
  { number: "50K+", label: "Active Users" },
  { number: "1,000+", label: "Templates" },
  { number: "100K+", label: "Downloads" },
  { number: "4.8★", label: "User Rating" },
]

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <Badge className="mb-4 bg-blue-600 text-white">About TemplateHub</Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Empowering Everyone to Create
            <span className="text-blue-600 block">Professional Presentations</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            We believe that great design shouldn't be limited to those with extensive design experience. Our mission is
            to provide beautiful, professional presentation templates that anyone can use to tell their story
            effectively.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700" asChild>
              <Link href="/browse">Explore Templates</Link>
            </Button>
            <Button size="lg" variant="outline" className="bg-white" asChild>
              <Link href="/contact">Get in Touch</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">{stat.number}</div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Story</h2>
            <p className="text-lg text-gray-600">How we started and where we're going</p>
          </div>

          <Card>
            <CardContent className="p-8">
              <div className="prose prose-gray max-w-none">
                <p className="text-lg text-gray-700 leading-relaxed mb-6">
                  TemplateHub was born from a simple frustration: why was it so hard to create professional-looking
                  presentations without spending hours on design? Our founder, Sarah Johnson, experienced this challenge
                  firsthand while working as a consultant, constantly struggling to create compelling presentations
                  under tight deadlines.
                </p>

                <p className="text-gray-700 leading-relaxed mb-6">
                  After years of working with Fortune 500 companies and seeing the same design challenges across
                  industries, Sarah realized there was a gap in the market. Small businesses, startups, students, and
                  professionals needed access to the same quality of design that large corporations could afford, but in
                  an accessible, affordable format.
                </p>

                <p className="text-gray-700 leading-relaxed mb-6">
                  In 2020, we launched TemplateHub with a simple mission: democratize professional presentation design.
                  We started with 50 carefully crafted templates and have grown to over 1,000 designs, serving more than
                  50,000 users worldwide.
                </p>

                <p className="text-gray-700 leading-relaxed">
                  Today, we're proud to be the go-to resource for professionals who want to create impactful
                  presentations without the complexity of starting from scratch. Every template in our library is
                  designed with real-world use cases in mind, tested by our community, and optimized for maximum impact.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card>
              <CardContent className="p-8 text-center">
                <Target className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Mission</h3>
                <p className="text-gray-700 leading-relaxed">
                  To empower individuals and businesses worldwide with professional-quality presentation templates that
                  save time, enhance communication, and drive results. We believe everyone deserves access to great
                  design, regardless of their budget or design experience.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-8 text-center">
                <Lightbulb className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Vision</h3>
                <p className="text-gray-700 leading-relaxed">
                  To become the world's leading platform for presentation templates, fostering a global community where
                  great ideas are communicated effectively through beautiful, accessible design. We envision a world
                  where compelling presentations are the norm, not the exception.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Values</h2>
            <p className="text-lg text-gray-600">The principles that guide everything we do</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <value.icon className="h-10 w-10 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">{value.title}</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Journey</h2>
            <p className="text-lg text-gray-600">Key milestones in our growth</p>
          </div>

          <div className="space-y-8">
            {milestones.map((milestone, index) => (
              <div key={index} className="flex items-start gap-6">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                    {milestone.year}
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{milestone.title}</h3>
                  <p className="text-gray-700 leading-relaxed">{milestone.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Meet Our Team</h2>
            <p className="text-lg text-gray-600">The passionate people behind TemplateHub</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="relative w-24 h-24 mx-auto mb-4">
                    <Image
                      src={member.image || "/placeholder.svg"}
                      alt={member.name}
                      fill
                      className="rounded-full object-cover"
                    />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">{member.name}</h3>
                  <p className="text-blue-600 font-medium mb-3">{member.role}</p>
                  <p className="text-gray-600 text-sm leading-relaxed mb-4">{member.bio}</p>
                  <div className="flex justify-center gap-3">
                    <Button variant="outline" size="sm" className="bg-transparent" asChild>
                      <Link href={member.linkedin}>LinkedIn</Link>
                    </Button>
                    <Button variant="outline" size="sm" className="bg-transparent" asChild>
                      <Link href={member.twitter}>Twitter</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Awards & Recognition */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-6xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Awards & Recognition</h2>
          <p className="text-lg text-gray-600 mb-12">Honored to be recognized by the design community</p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="flex flex-col items-center">
              <Award className="h-12 w-12 text-yellow-500 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Best Design Tool 2023</h3>
              <p className="text-gray-600 text-sm">Design Awards Association</p>
            </div>
            <div className="flex flex-col items-center">
              <Users className="h-12 w-12 text-blue-600 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Top User Choice</h3>
              <p className="text-gray-600 text-sm">ProductHunt Community</p>
            </div>
            <div className="flex flex-col items-center">
              <Globe className="h-12 w-12 text-green-600 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Global Impact Award</h3>
              <p className="text-gray-600 text-sm">Small Business Foundation</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 bg-gradient-to-r from-blue-600 to-indigo-600">
        <div className="max-w-6xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Ready to Create Something Amazing?</h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of professionals who trust TemplateHub for their presentation needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100" asChild>
              <Link href="/browse">Start Browsing Templates</Link>
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-blue-600 bg-transparent"
              asChild
            >
              <Link href="/auth/register">Create Free Account</Link>
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
