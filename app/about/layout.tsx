import type React from "react"
import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "About Us - TemplateHub",
  description:
    "Learn about TemplateHub's mission to democratize professional presentation design. Meet our team, discover our story, and see how we're helping professionals worldwide create better presentations.",
  keywords: "about TemplateHub, company story, presentation design, team, mission, values",
  openGraph: {
    title: "About TemplateHub - Our Story & Mission",
    description:
      "Discover how TemplateHub is empowering professionals worldwide with beautiful, accessible presentation templates.",
    type: "website",
  },
}

export default function AboutLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
