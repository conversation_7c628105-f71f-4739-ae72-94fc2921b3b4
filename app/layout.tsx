import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import { AuthProvider } from "@/hooks/use-auth"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "TemplateHub - Professional Presentation Templates",
  description:
    "Download stunning, ready-to-use presentation templates for PowerPoint, Google Slides, Keynote, and Canva. Trusted by professionals worldwide.",
  keywords:
    "presentation templates, PowerPoint templates, Google Slides templates, Keynote templates, Canva templates, business presentations",
  authors: [{ name: "TemplateHub" }],
  openGraph: {
    title: "TemplateHub - Professional Presentation Templates",
    description:
      "Download stunning, ready-to-use presentation templates for PowerPoint, Google Slides, Keynote, and Canva.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "TemplateHub - Professional Presentation Templates",
    description:
      "Download stunning, ready-to-use presentation templates for PowerPoint, Google Slides, Keynote, and Canva.",
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>{children}</AuthProvider>
      </body>
    </html>
  )
}
