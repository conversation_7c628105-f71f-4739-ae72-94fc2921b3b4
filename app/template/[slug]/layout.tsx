import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { fetchTemplateBySlug, fetchAllTemplates } from "@/lib/api"
import { Template } from "@/lib/templates-data"

export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  try {
    const template = await fetchTemplateBySlug(params.slug)

    if (!template) {
      return {
        title: "Template Not Found - TemplateHub",
        description: "The template you're looking for could not be found.",
      }
    }

    return {
      title: `${template.title} - TemplateHub`,
      description: template.description,
      keywords: template.tags.join(", "),
      openGraph: {
        title: `${template.title} - TemplateHub`,
        description: template.description,
        images: [template.thumbnail],
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title: `${template.title} - TemplateHub`,
        description: template.description,
        images: [template.thumbnail],
      },
    }
  } catch (error) {
    console.error('Error fetching template metadata:', error)
    return {
      title: "Template Error - TemplateHub",
      description: "There was an error loading the template.",
    }
  }
}

export async function generateStaticParams() {
  try {
    // Fetch all templates from API and generate static params for their slugs
    const templates = await fetchAllTemplates()
    return templates.map((template: Template) => ({
      slug: template.slug
    }))
  } catch (error) {
    console.error('Error fetching templates for static params:', error)
    // Return a minimal set of slugs in case of error
    return []
  }
}

export default function TemplateLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
