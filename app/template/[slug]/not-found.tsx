import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="max-w-7xl mx-auto px-4 py-16 text-center">
        <div className="max-w-md mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Template Not Found</h1>
          <p className="text-gray-600 mb-8">
            Sorry, the template you're looking for doesn't exist or may have been removed.
          </p>
          <div className="space-y-4">
            <Link href="/browse">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                Browse All Templates
              </Button>
            </Link>
            <div>
              <Link href="/" className="text-blue-600 hover:text-blue-700">
                Return to Homepage
              </Link>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  )
}
