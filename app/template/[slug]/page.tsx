"use client"

import { useState, useEffect, use } from "react"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { Star, Download, ExternalLink, Calendar, Tag, ChevronLeft, ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { PresentationCard } from "@/components/presentation-card"
import { type Template } from "@/lib/templates-data"
import { fetchTemplateBySlug, fetchRelatedTemplates, downloadTemplate } from "@/lib/api"

function StarRating({ rating, reviewCount }: { rating: number; reviewCount: number }) {
  return (
    <div className="flex items-center gap-2">
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-5 w-5 ${star <= rating ? "fill-yellow-400 text-yellow-400" : "fill-gray-200 text-gray-200"}`}
          />
        ))}
      </div>
      <span className="text-sm text-gray-600">
        {rating} ({reviewCount} reviews)
      </span>
    </div>
  )
}

function FormatBadges({ formats }: { formats: string[] }) {
  const formatColors = {
    PowerPoint: "bg-orange-100 text-orange-800 border-orange-200",
    "Google Slides": "bg-blue-100 text-blue-800 border-blue-200",
    Keynote: "bg-gray-100 text-gray-800 border-gray-200",
    Canva: "bg-purple-100 text-purple-800 border-purple-200",
  }

  return (
    <div className="flex flex-wrap gap-2">
      {formats.map((format) => (
        <Badge
          key={format}
          variant="outline"
          className={`${formatColors[format as keyof typeof formatColors] || "bg-gray-100 text-gray-800 border-gray-200"}`}
        >
          {format}
        </Badge>
      ))}
    </div>
  )
}

function SlidePreview({ slides }: { slides: Template["slides"] }) {
  const [currentSlide, setCurrentSlide] = useState(0)

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
  }

  const goToSlide = (index: number) => {
    setCurrentSlide(index)
  }

  return (
    <div className="bg-white shadow-sm overflow-hidden">
      {/* Main Preview */}
      <div className="relative">
        <div className="aspect-video relative bg-gray-100">
          <Image
            src={slides[currentSlide]?.thumbnail || "/placeholder.svg"}
            alt={`Slide ${currentSlide + 1} preview`}
            fill
            className="object-cover"
            priority
          />

          {/* Navigation Arrows */}
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 p-2 shadow-lg transition-all duration-200 hover:scale-110"
            disabled={slides.length <= 1}
          >
            <ChevronLeft className="h-6 w-6" />
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-700 p-2 shadow-lg transition-all duration-200 hover:scale-110"
            disabled={slides.length <= 1}
          >
            <ChevronRight className="h-6 w-6" />
          </button>

          {/* Slide Counter */}
          <div className="absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 text-sm">
            {currentSlide + 1} / {slides.length}
          </div>
        </div>
      </div>

      {/* Thumbnails */}
      <div className="p-4 bg-gray-50">
        <div className="flex gap-3 overflow-x-auto pb-2">
          {slides.map((slide, index) => (
            <button
              key={slide.id}
              onClick={() => goToSlide(index)}
              className={`flex-shrink-0 transition-all duration-200 ${
                index === currentSlide
                  ? "ring-2 ring-blue-500 ring-offset-2"
                  : "hover:ring-2 hover:ring-gray-300 hover:ring-offset-2"
              }`}
            >
              <div className="w-32 aspect-video relative bg-gray-100">
                <Image src={slide.thumbnail || "/placeholder.svg"} alt={`Slide ${index + 1} thumbnail`} fill className="object-cover" />
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}

export default function TemplatePage({ params }: { params: Promise<{ slug: string }> }) {
  const [template, setTemplate] = useState<Template | null>(null)
  const [relatedTemplates, setRelatedTemplates] = useState<Template[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [downloading, setDownloading] = useState(false)

  // Unwrap the params Promise
  const { slug } = use(params)

  useEffect(() => {
    async function fetchTemplateData() {
      try {
        setLoading(true)
        const templateData = await fetchTemplateBySlug(slug)

        if (!templateData) {
          notFound()
          return // This ensures TypeScript knows we won't proceed if templateData is null
        }

        setTemplate(templateData)

        // Fetch related templates
        const related = await fetchRelatedTemplates(
          slug,
          templateData.category || '',
          3
        )
        setRelatedTemplates(related)
      } catch (err) {
        console.error('Error fetching template data:', err)
        setError('Failed to load template data. Please try again later.')
      } finally {
        setLoading(false)
      }
    }

    fetchTemplateData()
  }, [slug])

  const handleDownload = async () => {
    if (!template) return

    setDownloading(true)
    try {
      const result = await downloadTemplate(slug)

      if (result.success && result.downloadUrl) {
        // Create a temporary link to trigger the download
        // The backend endpoint will handle proper Content-Disposition headers
        const link = document.createElement('a')
        link.href = result.downloadUrl
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        console.log('Download started successfully')
      } else {
        alert(result.error || 'Download failed. Please try again.')
      }
    } catch (error) {
      console.error('Download error:', error)
      alert('Download failed. Please try again.')
    } finally {
      setDownloading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg">Loading template...</p>
        </div>
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-500 text-lg">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-4 px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }
  
  if (!template) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-blue-600">
              Home
            </Link>
            <span>/</span>
            <Link href="/browse" className="hover:text-blue-600">
              Browse
            </Link>
            <span>/</span>
            <Link href={`/browse?category=${template.category}`} className="hover:text-blue-600">
              {template.category}
            </Link>
            <span>/</span>
            <span className="text-gray-900">{template.title}</span>
          </nav>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content - Left Side */}
          <div className="lg:col-span-2 space-y-8">
            {/* Slide Preview with Navigation */}
            <SlidePreview slides={template.slides} />

            {/* Template Badges */}
            <div className="flex items-center gap-3">
              <Badge className={template.isPremium ? "bg-yellow-600 text-white" : "bg-green-600 text-white"}>
                {template.isPremium ? "PREMIUM" : "FREE"}
              </Badge>
              <Badge variant="secondary" className="bg-gray-100 text-gray-700">
                {template.category}
              </Badge>
            </div>

            {/* Description */}
            <div className="bg-white shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Description</h3>
              <p className="text-gray-700 leading-relaxed mb-6">{template.description}</p>

              <h4 className="text-md font-semibold text-gray-900 mb-3">Features</h4>
              <ul className="space-y-2">
                {template.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2 text-gray-700">
                    <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            {/* Tags */}
            <div className="bg-white shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Tag className="h-5 w-5" />
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                {template.tags.map((tag) => (
                  <Link key={tag} href={`/browse?tag=${tag}`}>
                    <Badge
                      variant="outline"
                      className="hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300 cursor-pointer"
                    >
                      {tag}
                    </Badge>
                  </Link>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar - Right Side */}
          <div className="space-y-6">
            {/* Template Info */}
            <Card className="p-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">{template.title}</h1>

              <div className="space-y-4">
                <StarRating rating={template.rating} reviewCount={template.reviewCount} />

                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Download className="h-4 w-4" />
                  <span>{template.downloads} downloads</span>
                </div>

                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Calendar className="h-4 w-4" />
                  <span>Added {new Date(template.dateAdded).toLocaleDateString()}</span>
                </div>

                <Separator />

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Available Formats</h4>
                  <FormatBadges formats={template.formats} />
                </div>

                <Separator />

                {/* Download Buttons */}
                <div className="space-y-3">
                  <Button
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                    size="lg"
                    onClick={handleDownload}
                    disabled={downloading || !template.template_file}
                  >
                    <Download className="h-5 w-5 mr-2" />
                    {downloading
                      ? "Downloading..."
                      : !template.template_file
                        ? "No File Available"
                        : template.isPremium
                          ? "Download Premium"
                          : "Download Free"
                    }
                  </Button>

                  {template.formats.includes("Canva") && template.canvaUrl && (
                    <Button variant="outline" className="w-full bg-transparent" size="lg" asChild>
                      <Link href={template.canvaUrl} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="h-5 w-5 mr-2" />
                        Open in Canva
                      </Link>
                    </Button>
                  )}
                </div>
              </div>
            </Card>

            {/* Quick Actions */}
            <Card className="p-6">
              <h4 className="font-semibold text-gray-900 mb-4">Quick Actions</h4>
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start bg-transparent" size="sm">
                  <Star className="h-4 w-4 mr-2" />
                  Add to Favorites
                </Button>
                <Button variant="outline" className="w-full justify-start bg-transparent" size="sm">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Share Template
                </Button>
              </div>
            </Card>
          </div>
        </div>

        {/* Related Templates */}
        {relatedTemplates.length > 0 && (
          <section className="mt-16">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Related Templates</h2>
              <p className="text-gray-600">More {template.category.toLowerCase()} templates you might like</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedTemplates.map((relatedTemplate) => (
                <PresentationCard key={relatedTemplate.id} presentation={relatedTemplate} />
              ))}
            </div>

            <div className="text-center mt-8">
              <Link href="/browse">
                <Button variant="outline" size="lg" className="px-8 bg-transparent">
                  Browse More Templates
                </Button>
              </Link>
            </div>
          </section>
        )}
      </div>

      <Footer />
    </div>
  )
}
