"use client"

import { useAuth } from "@/hooks/use-auth"
import { ProtectedRoute } from "@/components/auth/protected-route"
import { ProfileForm } from "@/components/auth/profile-form"
import { PasswordChangeForm } from "@/components/auth/password-change-form"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { User, Calendar, Mail, Shield } from "lucide-react"

export default function ProfilePage() {
  const { user } = useAuth()

  if (!user) {
    return null // ProtectedRoute will handle redirect
  }

  const getUserInitials = (user: any) => {
    if (!user) return "U"
    
    let name = ""
    if (user.first_name || user.last_name) {
      name = `${user.first_name || ""} ${user.last_name || ""}`.trim()
    } else if (user.username) {
      name = user.username
    } else {
      return "U"
    }
    
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }

  const getDisplayName = (user: any) => {
    if (user.first_name && user.last_name) {
      return `${user.first_name} ${user.last_name}`
    } else if (user.first_name) {
      return user.first_name
    } else if (user.username) {
      return user.username
    } else {
      return "User"
    }
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Profile Settings</h1>
            <p className="text-gray-600 mt-2">Manage your account information and security settings.</p>
          </div>

          {/* Profile Overview Card */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Account Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-start gap-6">
                <Avatar className="h-20 w-20">
                  <AvatarImage src={user.profile_image || "/placeholder.svg"} alt={getDisplayName(user)} />
                  <AvatarFallback className="bg-blue-600 text-white text-lg">
                    {getUserInitials(user)}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {getDisplayName(user)}
                  </h3>
                  
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Mail className="h-4 w-4" />
                      {user.email}
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Calendar className="h-4 w-4" />
                      Member since {new Date(user.date_joined).toLocaleDateString("en-US", { 
                        month: "long", 
                        year: "numeric" 
                      })}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-green-600" />
                      <Badge variant={user.is_active ? "default" : "destructive"}>
                        {user.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>
                  
                  {user.bio && (
                    <div className="mt-4">
                      <p className="text-sm text-gray-700">{user.bio}</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Profile Forms */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Profile Information Form */}
            <div>
              <ProfileForm user={user} />
            </div>

            {/* Password Change Form */}
            <div>
              <PasswordChangeForm />
            </div>
          </div>

          {/* Account Statistics */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Account Statistics</CardTitle>
              <CardDescription>
                Your activity and usage statistics.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">0</div>
                  <div className="text-sm text-gray-600">Templates Downloaded</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">Free</div>
                  <div className="text-sm text-gray-600">Current Plan</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">Unlimited</div>
                  <div className="text-sm text-gray-600">Downloads Remaining</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ProtectedRoute>
  )
}
