import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { Card, CardContent } from "@/components/ui/card"

export default function TermsOfServicePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-6xl mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Terms of Service</h1>
          <p className="text-lg text-gray-600">Last updated: January 1, 2024</p>
        </div>

        <Card>
          <CardContent className="p-8 prose prose-gray max-w-none">
            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">1. Acceptance of Terms</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                By accessing and using TemplateHub ("the Service"), you accept and agree to be bound by the terms and
                provision of this agreement. If you do not agree to abide by the above, please do not use this service.
              </p>
              <p className="text-gray-700 leading-relaxed">
                These Terms of Service ("Terms") govern your use of our website located at templatehub.com (the
                "Service") operated by TemplateHub ("us", "we", or "our").
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">2. Description of Service</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                TemplateHub provides a platform for downloading presentation templates for various software including
                PowerPoint, Google Slides, Keynote, and Canva. Our service includes both free and premium templates.
              </p>
              <ul className="list-disc pl-6 text-gray-700 space-y-2">
                <li>Access to free presentation templates</li>
                <li>Premium template downloads with subscription</li>
                <li>Template customization guidelines and support</li>
                <li>User account management and download history</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">3. User Accounts</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                To access certain features of the Service, you may be required to create an account. You are responsible
                for:
              </p>
              <ul className="list-disc pl-6 text-gray-700 space-y-2 mb-4">
                <li>Maintaining the confidentiality of your account credentials</li>
                <li>All activities that occur under your account</li>
                <li>Providing accurate and complete information</li>
                <li>Updating your information to keep it current</li>
              </ul>
              <p className="text-gray-700 leading-relaxed">
                You must notify us immediately of any unauthorized use of your account or any other breach of security.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">4. Acceptable Use</h2>
              <p className="text-gray-700 leading-relaxed mb-4">You agree not to use the Service to:</p>
              <ul className="list-disc pl-6 text-gray-700 space-y-2 mb-4">
                <li>Violate any applicable laws or regulations</li>
                <li>Redistribute, resell, or share downloaded templates without proper licensing</li>
                <li>Attempt to gain unauthorized access to our systems</li>
                <li>Upload malicious code or engage in harmful activities</li>
                <li>Impersonate others or provide false information</li>
                <li>Use automated systems to access the Service without permission</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">5. Intellectual Property Rights</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                All content on TemplateHub, including templates, designs, text, graphics, logos, and software, is owned
                by TemplateHub or its licensors and is protected by copyright and other intellectual property laws.
              </p>
              <p className="text-gray-700 leading-relaxed mb-4">
                When you download templates, you receive a license to use them according to our License Agreement. You
                do not acquire ownership of the templates themselves.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">6. Payment and Subscriptions</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                Premium subscriptions are billed in advance on a monthly or annual basis. By subscribing, you agree to:
              </p>
              <ul className="list-disc pl-6 text-gray-700 space-y-2 mb-4">
                <li>Pay all charges associated with your subscription</li>
                <li>Automatic renewal unless cancelled</li>
                <li>Price changes with 30 days notice</li>
                <li>Our refund policy as outlined in our Refund Policy</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">7. Termination</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                We may terminate or suspend your account and access to the Service immediately, without prior notice,
                for any reason, including if you breach these Terms.
              </p>
              <p className="text-gray-700 leading-relaxed">
                You may terminate your account at any time by contacting us or through your account settings. Upon
                termination, your right to use the Service will cease immediately.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">8. Disclaimers and Limitation of Liability</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                The Service is provided "as is" without warranties of any kind. We do not guarantee that the Service
                will be uninterrupted, secure, or error-free.
              </p>
              <p className="text-gray-700 leading-relaxed">
                To the maximum extent permitted by law, TemplateHub shall not be liable for any indirect, incidental,
                special, or consequential damages arising from your use of the Service.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">9. Changes to Terms</h2>
              <p className="text-gray-700 leading-relaxed">
                We reserve the right to modify these Terms at any time. We will notify users of significant changes via
                email or through the Service. Your continued use of the Service after changes constitutes acceptance of
                the new Terms.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">10. Contact Information</h2>
              <p className="text-gray-700 leading-relaxed">
                If you have any questions about these Terms of Service, please contact us at:
              </p>
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <p className="text-gray-700">
                  <strong>Email:</strong> <EMAIL>
                  <br />
                  <strong>Address:</strong> 123 Template Street, Design City, DC 12345
                  <br />
                  <strong>Phone:</strong> (*************
                </p>
              </div>
            </section>
          </CardContent>
        </Card>
      </div>

      <Footer />
    </div>
  )
}
