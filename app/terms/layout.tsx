import type React from "react"
import type { Metada<PERSON> } from "next"

export const metadata: Metadata = {
  title: "Terms of Service - TemplateHub",
  description:
    "Read TemplateHub's Terms of Service to understand your rights and responsibilities when using our presentation template platform.",
  keywords: "terms of service, legal, user agreement, TemplateHub terms",
}

export default function TermsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
