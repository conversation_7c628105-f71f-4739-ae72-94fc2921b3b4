"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { useState, useEffect } from "react"
import { Grid3X3, List } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { CategoryCard } from "@/components/category-card"
import { LoadingState } from "@/components/ui/loading-state"
import { ErrorState } from "@/components/ui/error-state"
import { EmptyState } from "@/components/ui/empty-state"
import { PresentationCard } from "@/components/presentation-card"
import { fetchAllCategories, fetchFeaturedCategories, fetchTemplatesByCategory, Category } from "@/lib/api"
import { Template } from "@/lib/templates-data"

const sortOptions = [
  { value: "name", label: "Name A-Z" },
  { value: "templates", label: "Most Templates" },
  { value: "featured", label: "Featured First" },
]

export default function CategoriesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [sortBy, setSortBy] = useState("featured")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [showAll, setShowAll] = useState(false)
  
  // State for API data
  const [allCategories, setAllCategories] = useState<Category[]>([])
  const [featuredCategories, setFeaturedCategories] = useState<Category[]>([])
  const [popularTemplates, setPopularTemplates] = useState<{[key: string]: Template[]}>({})
  
  // Loading and error states
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Fetch data from API
  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true)
        setError(null)
        
        // Fetch all categories
        const categories = await fetchAllCategories()
        setAllCategories(categories)
        
        // Fetch featured categories
        const featured = await fetchFeaturedCategories()
        setFeaturedCategories(featured)
        
        // Fetch popular templates for each featured category
        const templatesMap: {[key: string]: Template[]} = {}
        for (const category of featured) {
          const templates = await fetchTemplatesByCategory(category.name)
          templatesMap[category.name] = templates
            .sort((a, b) => {
              const aDownloads = Number.parseInt(a.downloads.replace("k", "000").replace(".", ""))
              const bDownloads = Number.parseInt(b.downloads.replace("k", "000").replace(".", ""))
              return bDownloads - aDownloads
            })
            .slice(0, 4)
        }
        setPopularTemplates(templatesMap)
      } catch (err) {
        console.error('Error fetching category data:', err)
        setError('Failed to load categories. Please try again later.')
      } finally {
        setLoading(false)
      }
    }
    
    fetchData()
  }, [])
  
  // Filter and sort categories
  const filteredCategories = allCategories
    .filter((category: Category) => {
      if (searchQuery) {
        return (
          category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          category.description.toLowerCase().includes(searchQuery.toLowerCase())
        )
      }
      return true
    })
    .sort((a: Category, b: Category) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name)
        case "templates":
          return b.templateCount - a.templateCount
        case "featured":
        default:
          if (a.featured && !b.featured) return -1
          if (!a.featured && b.featured) return 1
          return b.templateCount - a.templateCount
      }
    })

  const displayedCategories = showAll ? filteredCategories : filteredCategories.slice(0, 8)
  
  // Get popular templates from each featured category
  const getPopularTemplatesFromCategory = (categoryName: string) => {
    return popularTemplates[categoryName] || []
  }

  const retryFetch = () => {
    setError(null)
    setLoading(true)
    // Re-trigger the useEffect
    window.location.reload()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-16 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Browse by <span className="text-blue-600">Category</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Discover professionally designed presentation templates organized by category. 
            Find the perfect template for your next presentation.
          </p>
          
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="11" cy="11" r="8"></circle>
                  <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
              </div>
              <Input
                placeholder="Search categories..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 py-3 text-lg bg-white"
                disabled={loading}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Featured Categories */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Featured Categories</h2>
            <p className="text-gray-600">Our most popular template categories</p>
          </div>

          {loading ? (
            <LoadingState message="Loading featured categories..." />
          ) : error ? (
            <ErrorState 
              message={error}
              onRetry={retryFetch}
              showRetry={true}
            />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {featuredCategories.map((category) => (
                <CategoryCard key={category.id} category={category} />
              ))}
            </div>
          )}
        </div>
      </section>

      {/* All Categories */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">All Categories</h2>
              {!loading && (
                <p className="text-gray-600">
                  Explore all {allCategories.length} template categories
                </p>
              )}
            </div>

            <div className="flex items-center gap-4 mt-4 md:mt-0">
              {/* Sort */}
              <Select value={sortBy} onValueChange={setSortBy} disabled={loading}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* View Mode */}
              <div className="flex border rounded-lg overflow-hidden">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-r-none"
                  disabled={loading}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="rounded-l-none"
                  disabled={loading}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {loading ? (
            <LoadingState message="Loading categories..." />
          ) : error ? (
            <ErrorState 
              message={error}
              onRetry={retryFetch}
              showRetry={true}
            />
          ) : filteredCategories.length === 0 ? (
            <EmptyState
              title="No categories found"
              message="Try adjusting your search terms"
              actionLabel="Clear Search"
              actionFn={() => setSearchQuery("")}
            />
          ) : (
            <>
              <div
                className={
                  viewMode === "grid"
                    ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                    : "space-y-4"
                }
              >
                {displayedCategories.map((category) => (
                  <CategoryCard key={category.id} category={category} />
                ))}
              </div>

              {/* Show More Button */}
              {!showAll && filteredCategories.length > 8 && (
                <div className="text-center mt-12">
                  <Button variant="outline" size="lg" onClick={() => setShowAll(true)}>
                    Show All Categories ({filteredCategories.length - 8} more)
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      {/* Popular Templates by Category */}
      <section className="py-16 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Popular Templates by Category</h2>
            <p className="text-gray-600">Discover the most downloaded templates from our featured categories.</p>
          </div>

          <div className="space-y-16">
            {featuredCategories.slice(0, 3).map((category) => {
              const categoryTemplates = getPopularTemplatesFromCategory(category.name)

              if (categoryTemplates.length === 0) return null

              return (
                <div key={category.id}>
                  <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{category.icon}</span>
                      <h3 className="text-2xl font-bold text-gray-900">{category.name}</h3>
                      <Badge variant="outline" className={category.color}>
                        {category.templateCount} templates
                      </Badge>
                    </div>
                    <Button variant="outline" className="bg-transparent" asChild>
                      <a href={`/browse?category=${category.name}`}>View All {category.name}</a>
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {categoryTemplates.map((template) => (
                      <PresentationCard key={template.id} presentation={template} />
                    ))}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Template Statistics</h2>
            <p className="text-gray-600">Our growing collection across all categories</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">{allCategories.length}</div>
              <div className="text-gray-600">Categories</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">100+</div>
              <div className="text-gray-600">Templates</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">4.8★</div>
              <div className="text-gray-600">Average Rating</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">50k+</div>
              <div className="text-gray-600">Downloads</div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
