import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"

export const metadata: Metadata = {
  title: "Template Categories - TemplateHub",
  description:
    "Browse presentation templates by category. Find business, marketing, creative, corporate, and more template categories for PowerPoint, Google Slides, Keynote, and Canva.",
  keywords: "template categories, presentation categories, business templates, marketing templates, creative templates",
}

export default function CategoriesLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
