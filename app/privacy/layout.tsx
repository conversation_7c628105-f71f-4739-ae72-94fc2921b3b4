import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"

export const metadata: Metadata = {
  title: "Privacy Policy - TemplateHub",
  description:
    "Learn how TemplateHub collects, uses, and protects your personal information. Our comprehensive privacy policy explains our data practices.",
  keywords: "privacy policy, data protection, personal information, GDPR, privacy rights",
}

export default function PrivacyLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
