import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"

export const metadata: Metadata = {
  title: "Contact Us - TemplateHub",
  description:
    "Get in touch with TemplateHub support team. We're here to help with questions, technical issues, billing, and feedback. Multiple contact methods available.",
  keywords: "contact TemplateHub, customer support, help, technical support, billing support",
  openGraph: {
    title: "Contact TemplateHub Support",
    description: "Need help? Contact our friendly support team via email, chat, or phone. We're here to assist you.",
    type: "website",
  },
}

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
