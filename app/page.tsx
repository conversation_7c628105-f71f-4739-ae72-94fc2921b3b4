"use client"

import React, { useState, useEffect, useMemo } from "react"
import { ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { PresentationCard } from "@/components/presentation-card"
import { fetchAllTemplates, fetchTemplatesByCategory } from "@/lib/api"
import { type Template } from "@/lib/templates-data"

const categories = ["All", "Business", "Marketing", "Creative", "Corporate", "Education"]

export default function HomePage() {
  const [allTemplates, setAllTemplates] = useState<Template[]>([])
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([])
  const [loading, setLoading] = useState(true)
  const [categoryLoading, setCategoryLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedCategory, setSelectedCategory] = useState("All")

  // Featured presentations (always first 4 from all templates)
  const featuredPresentations = allTemplates.slice(0, 4)

  // Load all templates on initial page load
  useEffect(() => {
    async function loadAllTemplates() {
      try {
        setLoading(true)
        setError(null)
        const data = await fetchAllTemplates()
        setAllTemplates(data)
        setFilteredTemplates(data.slice(0, 8)) // Show first 8 for "All" category
      } catch (err) {
        console.error('Error loading templates:', err)
        setError('Failed to load templates. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    loadAllTemplates()
  }, [])

  // Load templates by category when category changes
  useEffect(() => {
    async function loadTemplatesByCategory() {
      if (selectedCategory === "All") {
        setFilteredTemplates(allTemplates.slice(0, 8))
        return
      }

      try {
        setCategoryLoading(true)
        const data = await fetchTemplatesByCategory(selectedCategory)
        setFilteredTemplates(data.slice(0, 8))
      } catch (err) {
        console.error('Error loading templates by category:', err)
        setError('Failed to load templates for this category.')
      } finally {
        setCategoryLoading(false)
      }
    }

    if (allTemplates.length > 0) {
      loadTemplatesByCategory()
    }
  }, [selectedCategory, allTemplates])
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 to-indigo-100 py-20 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Professional Presentation
            <span className="text-blue-600 block">Templates</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Download stunning presentation templates for PowerPoint, Google Slides, Keynote, and Canva. Both free and
            premium options available.
          </p>
          <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg">
            Browse Templates
            <ChevronRight className="ml-2 h-5 w-5" />
          </Button>
        </div>

        {/* Background decoration */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-200 rounded-full opacity-20"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-200 rounded-full opacity-20"></div>
        </div>
      </section>

      {/* Featured Presentations */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Featured Templates</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Our most popular and highly-rated presentation templates, trusted by thousands of professionals worldwide.
            </p>
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-600 border-r-transparent align-[-0.125em]"></div>
              <p className="mt-4 text-gray-600">Loading templates...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-500 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()} variant="outline">
                Try Again
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {featuredPresentations.map((presentation: Template) => (
                <PresentationCard key={presentation.id} presentation={presentation} />
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Browse by Category */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Browse All Templates</h2>
              <p className="text-gray-600">
                Explore our complete collection of presentation templates across all platforms.
              </p>
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2 mt-4 md:mt-0">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className={
                    selectedCategory === category
                      ? "bg-blue-600 text-white hover:bg-blue-700"
                      : "hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300 bg-transparent"
                  }
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-blue-600 border-r-transparent align-[-0.125em]"></div>
              <p className="mt-4 text-gray-600">Loading templates...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-500 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()} variant="outline">
                Try Again
              </Button>
            </div>
          ) : (
            <>
              {categoryLoading ? (
                <div className="text-center py-12">
                  <div className="inline-block h-6 w-6 animate-spin rounded-full border-2 border-solid border-blue-600 border-r-transparent"></div>
                  <p className="mt-2 text-gray-600">Loading {selectedCategory} templates...</p>
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {filteredTemplates.map((presentation: Template) => (
                      <PresentationCard key={presentation.id} presentation={presentation} />
                    ))}
                  </div>

                  {filteredTemplates.length === 0 && selectedCategory !== "All" && (
                    <div className="text-center py-12">
                      <p className="text-gray-500 mb-4">No templates found in the "{selectedCategory}" category.</p>
                      <Button
                        variant="outline"
                        onClick={() => setSelectedCategory("All")}
                        className="bg-transparent"
                      >
                        View All Templates
                      </Button>
                    </div>
                  )}

                  {filteredTemplates.length > 0 && (
                    <div className="text-center mt-12">
                      <Button variant="outline" size="lg" className="px-8 bg-transparent">
                        Load More Templates
                      </Button>
                    </div>
                  )}
                </>
              )}
            </>
          )}
        </div>
      </section>

      {/* Trust Section */}
      <section className="py-16 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Trusted by Professionals</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-3xl font-bold text-blue-600 mb-2">Free & Premium</div>
              <div className="text-gray-600">Template Options</div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-3xl font-bold text-blue-600 mb-2">4.8★</div>
              <div className="text-gray-600">Average Rating</div>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-3xl font-bold text-blue-600 mb-2">500+</div>
              <div className="text-gray-600">Templates</div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
