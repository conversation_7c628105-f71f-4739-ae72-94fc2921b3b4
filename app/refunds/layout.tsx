import type React from "react"
import type { Metada<PERSON> } from "next"

export const metadata: Metadata = {
  title: "Refund Policy - TemplateHub",
  description:
    "Learn about TemplateHub's 30-day money-back guarantee and refund policy. Understand the refund process and eligibility requirements.",
  keywords: "refund policy, money back guarantee, subscription refund, billing, customer satisfaction",
}

export default function RefundsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
