import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { Card, CardContent } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle, XCircle, Clock, CreditCard } from "lucide-react"

export default function RefundPolicyPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-6xl mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Refund Policy</h1>
          <p className="text-lg text-gray-600">Your satisfaction is our priority</p>
          <p className="text-sm text-gray-500 mt-2">Last updated: January 1, 2024</p>
        </div>

        <Alert className="mb-8 border-blue-200 bg-blue-50">
          <CheckCircle className="h-4 w-4 text-blue-600" />
          <AlertDescription className="text-blue-800">
            <strong>30-Day Money-Back Guarantee:</strong> We offer a full refund within 30 days of purchase if you're
            not completely satisfied with our premium subscription.
          </AlertDescription>
        </Alert>

        <Card>
          <CardContent className="p-8 prose prose-gray max-w-none">
            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Refund Eligibility</h2>
              <p className="text-gray-700 leading-relaxed mb-6">
                We want you to be completely satisfied with your TemplateHub subscription. Our refund policy is designed
                to be fair and transparent for all users.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="border border-green-200 rounded-lg p-6 bg-green-50">
                  <div className="flex items-center gap-2 mb-3">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Eligible for Refund</h3>
                  </div>
                  <ul className="text-sm text-gray-700 space-y-2">
                    <li>• Request made within 30 days of purchase</li>
                    <li>• Technical issues preventing template access</li>
                    <li>• Billing errors or duplicate charges</li>
                    <li>• Subscription cancelled within trial period</li>
                    <li>• Service not as described</li>
                  </ul>
                </div>

                <div className="border border-red-200 rounded-lg p-6 bg-red-50">
                  <div className="flex items-center gap-2 mb-3">
                    <XCircle className="h-5 w-5 text-red-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Not Eligible for Refund</h3>
                  </div>
                  <ul className="text-sm text-gray-700 space-y-2">
                    <li>• Request made after 30 days</li>
                    <li>• Change of mind after downloading templates</li>
                    <li>• Violation of terms of service</li>
                    <li>• Fraudulent or abusive usage</li>
                    <li>• Free template downloads</li>
                  </ul>
                </div>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Refund Process</h2>

              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                    1
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Submit Refund Request</h3>
                    <p className="text-gray-700">
                      Contact our support team at <strong><EMAIL></strong> or through your account
                      dashboard. Include your order number and reason for the refund request.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                    2
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Review Process</h3>
                    <p className="text-gray-700">
                      Our team will review your request within 2-3 business days. We may contact you for additional
                      information or to help resolve any issues.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                    3
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Refund Processing</h3>
                    <p className="text-gray-700">
                      If approved, refunds are processed within 5-7 business days to your original payment method.
                      You'll receive a confirmation email once the refund is issued.
                    </p>
                  </div>
                </div>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Subscription Refunds</h2>

              <div className="space-y-6">
                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Clock className="h-5 w-5 text-blue-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Monthly Subscriptions</h3>
                  </div>
                  <p className="text-gray-700 mb-3">
                    Monthly subscribers can request a full refund within 30 days of their initial subscription or any
                    renewal payment.
                  </p>
                  <ul className="list-disc pl-6 text-gray-700 space-y-1">
                    <li>Full refund for current billing period if requested within 30 days</li>
                    <li>Subscription access continues until the end of the paid period</li>
                    <li>No partial refunds for unused portions of the month</li>
                  </ul>
                </div>

                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <CreditCard className="h-5 w-5 text-blue-600" />
                    <h3 className="text-lg font-semibold text-gray-900">Annual Subscriptions</h3>
                  </div>
                  <p className="text-gray-700 mb-3">
                    Annual subscribers receive enhanced refund protection due to the longer commitment period.
                  </p>
                  <ul className="list-disc pl-6 text-gray-700 space-y-1">
                    <li>Full refund if requested within 30 days of purchase</li>
                    <li>Prorated refund available for the first 90 days</li>
                    <li>After 90 days, no refunds but can cancel future renewals</li>
                  </ul>
                </div>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Special Circumstances</h2>

              <h3 className="text-xl font-semibold text-gray-900 mb-3">Technical Issues</h3>
              <p className="text-gray-700 leading-relaxed mb-4">
                If you experience technical problems that prevent you from accessing or using our templates, we'll work
                to resolve the issue first. If we cannot resolve the problem within a reasonable timeframe, a full
                refund will be provided regardless of the 30-day limit.
              </p>

              <h3 className="text-xl font-semibold text-gray-900 mb-3">Billing Errors</h3>
              <p className="text-gray-700 leading-relaxed mb-4">
                Duplicate charges, incorrect amounts, or unauthorized transactions will be refunded immediately upon
                verification. These refunds are not subject to our standard time limits.
              </p>

              <h3 className="text-xl font-semibold text-gray-900 mb-3">Service Interruptions</h3>
              <p className="text-gray-700 leading-relaxed">
                If our service is unavailable for extended periods due to maintenance or technical issues, we may
                provide account credits or refunds for the affected time period.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Refund Methods</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                Refunds are processed using the same payment method used for the original purchase:
              </p>
              <ul className="list-disc pl-6 text-gray-700 space-y-2">
                <li>
                  <strong>Credit/Debit Cards:</strong> 5-7 business days to appear on your statement
                </li>
                <li>
                  <strong>PayPal:</strong> 3-5 business days to your PayPal account
                </li>
                <li>
                  <strong>Bank Transfers:</strong> 7-10 business days depending on your bank
                </li>
                <li>
                  <strong>Digital Wallets:</strong> 3-5 business days to your wallet
                </li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Cancellation vs. Refund</h2>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Important Distinction</h3>
                <p className="text-gray-700 mb-3">
                  Cancelling your subscription is different from requesting a refund:
                </p>
                <ul className="list-disc pl-6 text-gray-700 space-y-2">
                  <li>
                    <strong>Cancellation:</strong> Stops future billing but you keep access until the current period
                    ends
                  </li>
                  <li>
                    <strong>Refund:</strong> Returns money for the current billing period and may end access immediately
                  </li>
                </ul>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Dispute Resolution</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                If you're not satisfied with our refund decision, you can:
              </p>
              <ul className="list-disc pl-6 text-gray-700 space-y-2">
                <li>Request a review by our customer service manager</li>
                <li>Escalate to our billing department for a second opinion</li>
                <li>Contact your payment provider to dispute the charge</li>
                <li>Seek resolution through applicable consumer protection agencies</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Contact Information</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                For refund requests or questions about our refund policy:
              </p>
              <div className="p-4 bg-gray-50 rounded-lg">
                <p className="text-gray-700">
                  <strong>Refund Requests:</strong> <EMAIL>
                  <br />
                  <strong>General Support:</strong> <EMAIL>
                  <br />
                  <strong>Phone:</strong> (*************
                  <br />
                  <strong>Hours:</strong> Monday-Friday, 9 AM - 6 PM EST
                </p>
              </div>

              <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-blue-800 text-sm">
                  <strong>Quick Tip:</strong> Include your order number, email address, and detailed reason for the
                  refund request to expedite the process.
                </p>
              </div>
            </section>
          </CardContent>
        </Card>
      </div>

      <Footer />
    </div>
  )
}
