import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"

export const metadata: Metadata = {
  title: "License Agreement - TemplateHub",
  description:
    "Understand your rights and restrictions when using TemplateHub presentation templates. Learn about commercial use, attribution requirements, and license types.",
  keywords: "license agreement, template license, usage rights, commercial use, attribution",
}

export default function LicenseLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
