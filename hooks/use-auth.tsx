"use client"

import { create<PERSON>ontext, use<PERSON>ontext, useState, useEffect, type <PERSON>actNode } from "react"
import { authAP<PERSON>, tokenManager, type User } from "@/lib/auth"

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (username: string, password: string) => Promise<void>
  logout: () => Promise<void>
  register: (userData: {
    username: string
    email: string
    password: string
    password_confirm: string
    first_name: string
    last_name: string
  }) => Promise<void>
  updateProfile: (data: Partial<User>) => Promise<void>
  changePassword: (oldPassword: string, newPassword: string) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check for existing session on mount
    const checkAuth = async () => {
      try {
        const currentUser = await authAPI.getCurrentUser()
        if (currentUser) {
          setUser(currentUser)
          // Store user in localStorage for quick access
          localStorage.setItem("user", JSON.stringify(currentUser))
        }
      } catch (error) {
        console.error("Auth check failed:", error)
        // Clear any invalid tokens
        tokenManager.clearTokens()
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = async (username: string, password: string) => {
    const { user: userData, tokens } = await authAPI.login(username, password)

    // Store tokens
    tokenManager.setTokens(tokens)

    // Store user data
    setUser(userData)
    localStorage.setItem("user", JSON.stringify(userData))
  }

  const logout = async () => {
    await authAPI.logout()
    setUser(null)
  }

  const register = async (userData: {
    username: string
    email: string
    password: string
    password_confirm: string
    first_name: string
    last_name: string
  }) => {
    const newUser = await authAPI.register(userData)
    // Don't auto-login after registration, require email verification
    return newUser
  }

  const updateProfile = async (data: Partial<User>) => {
    const updatedUser = await authAPI.updateProfile(data)
    setUser(updatedUser)
    localStorage.setItem("user", JSON.stringify(updatedUser))
  }

  const changePassword = async (oldPassword: string, newPassword: string) => {
    await authAPI.changePassword(oldPassword, newPassword)
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        logout,
        register,
        updateProfile,
        changePassword
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
