# User Flow Stories for Slides Project

This document outlines comprehensive user flow stories for the Slides project, detailing how users will interact with the application across various features. These stories will guide the implementation of the Django backend to support the Next.js frontend.

## 1. Authentication Flows

### 1.1 User Registration
1. **Initial Action**: User visits the homepage and clicks "Sign Up" or "Register"
2. **Form Interaction**: User fills out registration form with name, email, and password
3. **Submission**: User submits the form
4. **Backend Process**: 
   - Django validates form data
   - Creates new user record in database
   - Generates verification token
   - Sends verification email
5. **Response**: User receives success message with instructions to verify email
6. **Email Verification**: User clicks verification link in email
7. **Completion**: User account is activated and user is redirected to login page

### 1.2 User Login
1. **Initial Action**: User visits login page
2. **Form Interaction**: User enters email and password
3. **Submission**: User submits login form
4. **Backend Process**:
   - Django authenticates credentials
   - Generates JW<PERSON> token
   - Records login timestamp
5. **Response**: User is redirected to dashboard or previous page
6. **Session Management**: JWT token is stored for subsequent API requests

### 1.3 Password Reset
1. **Initial Action**: User clicks "Forgot Password" on login page
2. **Form Interaction**: User enters email address
3. **Submission**: User submits form
4. **Backend Process**:
   - Django validates email exists
   - Generates password reset token
   - Sends reset email
5. **Email Interaction**: User clicks reset link in email
6. **New Password**: User enters and confirms new password
7. **Completion**: Password is updated and user is redirected to login

## 2. Template Browsing & Discovery

### 2.1 Homepage Browsing
1. **Initial Load**: User visits homepage
2. **Content Display**:
   - Featured templates are displayed
   - Categories are shown for filtering
3. **Interaction**: User scrolls through featured templates
4. **Backend Process**: Django serves template data via API
5. **Response**: Templates load with images, titles, and basic info

### 2.2 Category Filtering
1. **Initial Action**: User clicks on a category button
2. **Backend Process**:
   - Django filters templates by selected category
   - Returns paginated results
3. **Response**: UI updates to show filtered templates
4. **Refinement**: User can apply additional filters (format, tags, etc.)

### 2.3 Search Functionality
1. **Initial Action**: User enters search term in search bar
2. **Submission**: User submits search query
3. **Backend Process**:
   - Django performs search across template titles, descriptions, and tags
   - Returns ranked results
4. **Response**: Search results display with highlighting of matched terms
5. **Refinement**: User can filter search results further

## 3. Template Interaction

### 3.1 Template Detail View
1. **Initial Action**: User clicks on a template card
2. **Navigation**: User is taken to template detail page
3. **Backend Process**: Django serves detailed template data
4. **Content Display**:
   - Template images/slides
   - Description and features
   - Available formats
   - Reviews and ratings
5. **Interaction**: User can browse through template slides

### 3.2 Template Download
1. **Initial Action**: User clicks "Download" button on template detail page
2. **Authentication Check**:
   - If not logged in, prompt for login
   - If premium template and user doesn't have access, show upgrade options
3. **Backend Process**:
   - Django validates user permissions
   - Records download in user history
   - Prepares file for download
4. **Response**: Template file begins downloading
5. **Follow-up**: User receives confirmation and optional next steps

### 3.3 Adding Template to Favorites
1. **Initial Action**: User clicks "Add to Favorites" button
2. **Authentication Check**: If not logged in, prompt for login
3. **Backend Process**:
   - Django adds template to user's favorites list
   - Updates favorites count for template
4. **Response**: UI updates to show template is favorited
5. **Navigation Option**: User can view all favorites from profile

## 4. Review System

### 4.1 Viewing Reviews
1. **Initial Action**: User scrolls to reviews section on template detail page
2. **Backend Process**: Django serves paginated reviews for the template
3. **Content Display**: Reviews show with ratings, comments, and user info
4. **Interaction**: User can sort reviews by rating, date, etc.

### 4.2 Writing a Review
1. **Initial Action**: User clicks "Write a Review" button
2. **Authentication Check**: If not logged in, prompt for login
3. **Form Interaction**: User enters rating and review text
4. **Submission**: User submits review
5. **Backend Process**:
   - Django validates review data
   - Saves review to database
   - Updates template average rating
6. **Response**: Review appears in reviews section
7. **Moderation**: Admin receives notification for review moderation (if enabled)

### 4.3 Editing/Deleting a Review
1. **Initial Action**: User clicks "Edit" on their existing review
2. **Form Interaction**: User modifies rating or review text
3. **Submission**: User submits updated review
4. **Backend Process**:
   - Django updates review in database
   - Recalculates template average rating
5. **Response**: Updated review appears in reviews section

## 5. User Profile & Dashboard

### 5.1 Viewing Profile
1. **Initial Action**: User clicks on profile icon/name
2. **Navigation**: User is taken to profile page
3. **Backend Process**: Django serves user profile data
4. **Content Display**:
   - User information
   - Download history
   - Favorite templates
   - Reviews written

### 5.2 Editing Profile
1. **Initial Action**: User clicks "Edit Profile" button
2. **Form Interaction**: User modifies profile information
3. **Submission**: User submits updated profile
4. **Backend Process**: Django validates and saves profile changes
5. **Response**: Profile page updates with new information

### 5.3 Managing Favorites
1. **Initial Action**: User navigates to "My Favorites" section
2. **Backend Process**: Django serves user's favorited templates
3. **Interaction**: User can view, organize, or remove favorites
4. **Response**: UI updates to reflect changes to favorites

## 6. Admin Flows

### 6.1 Template Management
1. **Initial Action**: Admin logs into admin dashboard
2. **Navigation**: Admin navigates to template management section
3. **Interaction Options**:
   - Add new template
   - Edit existing template
   - Remove template
   - Feature template on homepage
4. **Backend Process**: Django handles CRUD operations on templates
5. **Response**: Admin dashboard updates to reflect changes

### 6.2 User Management
1. **Initial Action**: Admin navigates to user management section
2. **Content Display**: List of users with key information
3. **Interaction Options**:
   - View user details
   - Edit user permissions
   - Disable/enable user accounts
4. **Backend Process**: Django handles user management operations
5. **Response**: User list updates to reflect changes

### 6.3 Review Moderation
1. **Initial Action**: Admin navigates to review moderation queue
2. **Content Display**: List of pending reviews
3. **Interaction Options**:
   - Approve review
   - Reject review
   - Edit review content
4. **Backend Process**: Django updates review status in database
5. **Response**: Review appears on site or remains hidden based on decision

## 7. Media Upload Flows

### 7.1 Template Upload (Admin)
1. **Initial Action**: Admin initiates template upload
2. **Form Interaction**: Admin enters template details and uploads files
3. **Backend Process**:
   - Django validates files
   - Processes images (creates thumbnails, optimizes)
   - Stores files in appropriate storage
4. **Response**: Template is created and available for users

### 7.2 Profile Image Upload
1. **Initial Action**: User clicks to update profile image
2. **File Selection**: User selects image file
3. **Backend Process**:
   - Django validates image
   - Resizes/crops as needed
   - Updates user profile
4. **Response**: Profile displays new image

## 8. API Integration Flows

### 8.1 Frontend-Backend Communication
1. **Initial Action**: User interacts with frontend (e.g., loads page, submits form)
2. **API Request**: Next.js sends request to Django API
3. **Authentication**: JWT token included in request header
4. **Backend Process**: Django processes request and prepares response
5. **Response**: Data returned to frontend
6. **UI Update**: Next.js updates UI with received data

### 8.2 Error Handling
1. **Trigger**: API request fails or returns error
2. **Backend Process**: Django returns appropriate error code and message
3. **Frontend Handling**: Next.js displays appropriate error message
4. **Recovery Options**: User is presented with options to retry or alternative actions

## 9. Payment Flows (For Premium Templates)

### 9.1 Purchasing Premium Template
1. **Initial Action**: User clicks "Purchase" on premium template
2. **Authentication Check**: If not logged in, prompt for login
3. **Payment Form**: User enters payment details
4. **Backend Process**:
   - Django integrates with payment processor
   - Validates payment
   - Records transaction
5. **Response**: User gains access to premium template
6. **Follow-up**: Email receipt sent to user

### 9.2 Subscription Management
1. **Initial Action**: User navigates to subscription settings
2. **Content Display**: Current subscription status and options
3. **Interaction Options**:
   - Upgrade subscription
   - Cancel subscription
   - Update payment method
4. **Backend Process**: Django handles subscription changes
5. **Response**: Subscription status updated

## 10. Notification Flows

### 10.1 Email Notifications
1. **Trigger Events**:
   - New account creation
   - Password reset
   - Purchase confirmation
   - New template in favorite category
2. **Backend Process**: Django generates and sends email
3. **User Action**: User receives and can interact with email

### 10.2 In-App Notifications
1. **Trigger Events**:
   - New review on user's template
   - Template download milestone
   - New template in favorite category
2. **Backend Process**: Django creates notification record
3. **Delivery**: Notification appears in user's notification center
4. **Interaction**: User can mark as read or take related action
