from django.core.management.base import BaseCommand
from django.utils.text import slugify
from templates.models import Category, Format, Tag

class Command(BaseCommand):
    help = 'Populates the database with initial categories, formats, and tags'

    def handle(self, *args, **kwargs):
        self.stdout.write('Populating initial data...')
        
        # Create categories
        categories_data = [
            {
                'name': 'Business',
                'slug': 'business',
                'description': 'Professional business presentations, pitch decks, and corporate templates for meetings and proposals.',
                'icon': '💼',
                'color': 'bg-blue-100 text-blue-800 border-blue-200',
                'featured': True,
            },
            {
                'name': 'Marketing',
                'slug': 'marketing',
                'description': 'Marketing strategy presentations, campaign decks, and promotional templates for agencies and marketers.',
                'icon': '📈',
                'color': 'bg-green-100 text-green-800 border-green-200',
                'featured': True,
            },
            {
                'name': 'Creative',
                'slug': 'creative',
                'description': 'Portfolio showcases, creative briefs, and artistic presentation templates for designers and artists.',
                'icon': '🎨',
                'color': 'bg-purple-100 text-purple-800 border-purple-200',
                'featured': True,
            },
            {
                'name': 'Corporate',
                'slug': 'corporate',
                'description': 'Annual reports, company overviews, and formal corporate presentation templates.',
                'icon': '🏢',
                'color': 'bg-gray-100 text-gray-800 border-gray-200',
                'featured': False,
            },
            {
                'name': 'Education',
                'slug': 'education',
                'description': 'Educational presentations, lesson plans, and academic templates for teachers and students.',
                'icon': '📚',
                'color': 'bg-yellow-100 text-yellow-800 border-yellow-200',
                'featured': False,
            },
        ]
        
        for category_data in categories_data:
            category, created = Category.objects.get_or_create(
                slug=category_data['slug'],
                defaults=category_data
            )
            if created:
                self.stdout.write(f'Created category: {category.name}')
            else:
                self.stdout.write(f'Category already exists: {category.name}')
        
        # Create formats
        formats_data = [
            'PowerPoint',
            'Google Slides',
            'Keynote',
            'PDF',
        ]
        
        for format_name in formats_data:
            format_obj, created = Format.objects.get_or_create(
                name=format_name
            )
            if created:
                self.stdout.write(f'Created format: {format_obj.name}')
            else:
                self.stdout.write(f'Format already exists: {format_obj.name}')
        
        # Create tags
        tags_data = [
            'business', 'pitch', 'modern', 'corporate', 'startup', 'professional',
            'creative', 'portfolio', 'design', 'art', 'photography',
            'marketing', 'sales', 'strategy', 'campaign', 'promotion',
            'education', 'academic', 'school', 'university', 'course',
            'minimal', 'clean', 'elegant', 'colorful', 'dark', 'light',
            'infographic', 'data', 'chart', 'timeline', 'roadmap',
            'proposal', 'report', 'analysis', 'overview', 'summary',
        ]
        
        for tag_name in tags_data:
            tag_slug = slugify(tag_name)
            tag, created = Tag.objects.get_or_create(
                slug=tag_slug,
                defaults={
                    'name': tag_name,
                }
            )
            if created:
                self.stdout.write(f'Created tag: {tag.name}')
            else:
                self.stdout.write(f'Tag already exists: {tag.name}')
        
        self.stdout.write(self.style.SUCCESS('Successfully populated initial data!'))
