from django.core.management.base import BaseCommand
from django.utils.text import slugify
from templates.models import Category, Format, Tag, Template, TemplateImage
import random
from django.core.files.base import ContentFile
import base64
from io import BytesIO
from PIL import Image, ImageDraw, ImageFont
import os

class Command(BaseCommand):
    help = 'Creates sample templates with slides'

    def create_placeholder_image(self, width, height, text):
        """Create a simple placeholder image with text"""
        img = Image.new('RGB', (width, height), color=(73, 109, 137))
        d = ImageDraw.Draw(img)
        
        # Try to use a system font, fallback to default if not available
        try:
            font = ImageFont.truetype("Arial.ttf", 36)
        except IOError:
            font = ImageFont.load_default()
            
        d.text((width/2, height/2), text, fill=(255, 255, 255), anchor="mm", font=font)
        
        # Save to BytesIO object
        buffer = BytesIO()
        img.save(buffer, format="JPEG")
        buffer.seek(0)
        
        return buffer

    def handle(self, *args, **kwargs):
        self.stdout.write('Creating sample templates...')
        
        # Get existing categories, formats, and tags
        business_category = Category.objects.get(slug='business')
        creative_category = Category.objects.get(slug='creative')
        
        powerpoint_format = Format.objects.get(name='PowerPoint')
        google_slides_format = Format.objects.get(name='Google Slides')
        keynote_format = Format.objects.get(name='Keynote')
        
        business_tags = Tag.objects.filter(slug__in=['business', 'pitch', 'modern', 'corporate', 'professional'])
        creative_tags = Tag.objects.filter(slug__in=['creative', 'portfolio', 'design', 'minimal', 'elegant'])
        
        # Create sample templates
        templates_data = [
            {
                'title': 'Modern Business Pitch Deck',
                'slug': 'modern-business-pitch-deck',
                'category': business_category,
                'formats': [powerpoint_format, google_slides_format, keynote_format],
                'tags': business_tags,
                'description': 'A sleek and modern business pitch deck template designed for startups and established companies. This comprehensive template includes professionally designed slides covering all essential aspects of a business presentation.',
                'features': ['25+ professionally designed slides', 'Easy to customize colors and fonts', 'Includes charts and infographics', 'Master slide layouts included'],
                'is_premium': False,
                'canva_url': 'https://canva.com/templates/business-pitch-deck',
                'slides': [
                    'Title Slide',
                    'Problem Statement',
                    'Solution Overview',
                    'Market Analysis',
                    'Business Model',
                    'Financial Projections',
                    'Team Introduction',
                    'Funding Request',
                ]
            },
            {
                'title': 'Creative Portfolio Showcase',
                'slug': 'creative-portfolio-showcase',
                'category': creative_category,
                'formats': [powerpoint_format, google_slides_format],
                'tags': creative_tags,
                'description': 'A stunning portfolio template for creatives, designers, photographers, and artists. Showcase your work with elegant layouts and modern design elements.',
                'features': ['20+ creative slide layouts', 'Gallery and portfolio sections', 'Minimal and elegant design', 'Easy to customize'],
                'is_premium': True,
                'canva_url': 'https://canva.com/templates/portfolio-showcase',
                'slides': [
                    'Cover Slide',
                    'About Me',
                    'Skills & Expertise',
                    'Portfolio Gallery',
                    'Project Showcase 1',
                    'Project Showcase 2',
                    'Testimonials',
                    'Contact Information',
                ]
            }
        ]
        
        for template_data in templates_data:
            # Check if template already exists
            if Template.objects.filter(slug=template_data['slug']).exists():
                self.stdout.write(f"Template '{template_data['title']}' already exists, skipping...")
                continue
                
            # Create template
            template = Template.objects.create(
                title=template_data['title'],
                slug=template_data['slug'],
                category=template_data['category'],
                description=template_data['description'],
                features=template_data['features'],
                is_premium=template_data['is_premium'],
                canva_url=template_data['canva_url'],
            )
            
            # Add formats
            template.formats.set(template_data['formats'])
            
            # Add tags
            template.tags.set(template_data['tags'])
            
            # Create thumbnail
            thumbnail_buffer = self.create_placeholder_image(600, 400, f"{template_data['title']} Thumbnail")
            template.thumbnail.save(f"{template_data['slug']}-thumbnail.jpg", ContentFile(thumbnail_buffer.read()), save=True)
            
            # Create slides
            for i, slide_title in enumerate(template_data['slides'], 1):
                slide_buffer = self.create_placeholder_image(640, 360, slide_title)
                slide = TemplateImage(
                    template=template,
                    order=i
                )
                slide.image.save(f"{template_data['slug']}-slide-{i}.jpg", ContentFile(slide_buffer.read()), save=True)
                self.stdout.write(f"Created slide {i}: {slide_title}")
            
            self.stdout.write(f"Created template: {template.title} with {len(template_data['slides'])} slides")
        
        self.stdout.write(self.style.SUCCESS('Successfully created sample templates!'))
