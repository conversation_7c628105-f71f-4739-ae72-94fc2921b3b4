from django.db import models
from django.utils.text import slugify
from django.conf import settings

class Category(models.Model):
    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=100, unique=True)
    description = models.TextField()
    icon = models.Char<PERSON>ield(max_length=10, help_text="Emoji or icon identifier")
    color = models.CharField(max_length=100, help_text="CSS color classes")
    featured = models.BooleanField(default=False)
    
    class Meta:
        verbose_name_plural = "Categories"
        ordering = ['name']
        
    def __str__(self):
        return self.name
        
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)
        
    def template_count(self):
        return self.templates.count()

class Format(models.Model):
    name = models.CharField(max_length=50, unique=True)
    
    def __str__(self):
        return self.name

class Tag(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=50, unique=True)
    slug = models.SlugField(max_length=50, unique=True)
    
    def __str__(self):
        return self.name
        
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

class Template(models.Model):
    # Basic identification
    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True)
    
    # Relationships
    category = models.ForeignKey(Category, related_name='templates', on_delete=models.PROTECT)
    formats = models.ManyToManyField(Format, related_name='templates')
    tags = models.ManyToManyField(Tag, related_name='templates', blank=True)
    
    # Content
    description = models.TextField()
    thumbnail = models.ImageField(upload_to='template_thumbnails/')
    template_file = models.FileField(upload_to='template_files/', null=True, blank=True, help_text="Uploadable template file")
    features = models.JSONField(default=list, blank=True, help_text="List of template features")
    
    # Business model
    is_premium = models.BooleanField(default=False)
    
    # External integrations
    canva_url = models.URLField(blank=True, null=True)
    
    # Metadata
    date_added = models.DateTimeField(auto_now_add=True)
    last_modified = models.DateTimeField(auto_now=True)
    download_count = models.PositiveIntegerField(default=0)
    
    class Meta:
        ordering = ['-date_added']
        
    def __str__(self):
        return self.title
        
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)
        
    @property
    def rating(self):
        reviews = self.reviews.all()
        if not reviews:
            return 0
        return sum(review.rating for review in reviews) / reviews.count()
        
    @property
    def review_count(self):
        return self.reviews.count()

class TemplateImage(models.Model):
    template = models.ForeignKey(Template, related_name='images', on_delete=models.CASCADE)
    image = models.ImageField(upload_to='template_gallery/')
    order = models.PositiveIntegerField(default=0)
    alt_text = models.CharField(max_length=255, blank=True, help_text="Alternative text for accessibility")
    
    class Meta:
        ordering = ['order']
        
    def __str__(self):
        return f"Image for {self.template.title}"
        
    def save(self, *args, **kwargs):
        # Auto-assign order if not specified
        if self.order == 0:
            last_image = TemplateImage.objects.filter(template=self.template).order_by('-order').first()
            self.order = 1 if last_image is None else last_image.order + 1
        super().save(*args, **kwargs)

class Review(models.Model):
    template = models.ForeignKey(Template, related_name='reviews', on_delete=models.CASCADE)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, related_name='reviews', on_delete=models.CASCADE)
    rating = models.PositiveSmallIntegerField(choices=[(i, i) for i in range(1, 6)])
    comment = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ['template', 'user']
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.user.username}'s review of {self.template.title}"

class Favorite(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, related_name='favorites', on_delete=models.CASCADE)
    template = models.ForeignKey(Template, related_name='favorited_by', on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['user', 'template']
        
    def __str__(self):
        return f"{self.user.username}'s favorite: {self.template.title}"

