from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from .views import (
    CategoryViewSet, FormatViewSet, TagViewSet,
    TemplateViewSet, ReviewViewSet, FavoriteViewSet
)

router = DefaultRouter()
router.register('categories', CategoryViewSet)
router.register('formats', FormatViewSet)
router.register('tags', TagViewSet)
router.register('templates', TemplateViewSet)
router.register('reviews', ReviewViewSet)
router.register('favorites', FavoriteViewSet, basename='favorite')

urlpatterns = [
    path('', include(router.urls)),
]
