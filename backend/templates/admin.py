from django.contrib import admin
from django import forms
from .models import Category, Format, Tag, Template, TemplateImage, Review, Favorite


class TemplateAdminForm(forms.ModelForm):
    features_text = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 5, 'cols': 50}),
        help_text="Enter template features, one per line. Example:\n• Professional design\n• Easy to customize\n• Multiple layouts",
        required=False,
        label="Features"
    )

    class Meta:
        model = Template
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Convert JSON list to text (one feature per line)
        if self.instance and self.instance.features:
            self.fields['features_text'].initial = '\n'.join(self.instance.features)
        # Hide the original features field
        if 'features' in self.fields:
            self.fields['features'].widget = forms.HiddenInput()

    def clean_features_text(self):
        features_text = self.cleaned_data.get('features_text', '')
        if features_text:
            # Handle both string and list inputs
            if isinstance(features_text, list):
                return features_text
            # Split by lines and clean up
            features = [line.strip() for line in features_text.split('\n') if line.strip()]
            return features
        return []

    def save(self, commit=True):
        instance = super().save(commit=False)
        # Set the features from the text field
        instance.features = self.clean_features_text()
        if commit:
            instance.save()
        return instance


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'featured', 'template_count')
    prepopulated_fields = {'slug': ('name',)}
    search_fields = ('name', 'description')
    list_filter = ('featured',)

@admin.register(Format)
class FormatAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Tag)
class TagAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug')
    prepopulated_fields = {'slug': ('name',)}
    search_fields = ('name',)

class TemplateImageInline(admin.TabularInline):
    model = TemplateImage
    extra = 1
    fields = ('image', 'order', 'alt_text')

@admin.register(Template)
class TemplateAdmin(admin.ModelAdmin):
    form = TemplateAdminForm
    list_display = ('title', 'category', 'is_premium', 'date_added', 'download_count', 'rating', 'review_count')
    list_filter = ('category', 'is_premium', 'formats', 'tags')
    search_fields = ('title', 'description')
    prepopulated_fields = {'slug': ('title',)}
    readonly_fields = ('date_added', 'last_modified', 'download_count')
    filter_horizontal = ('formats', 'tags')
    inlines = [TemplateImageInline]
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'slug', 'category', 'description', 'thumbnail', 'template_file')
        }),
        ('Classifications', {
            'fields': ('formats', 'tags')
        }),
        ('Features', {
            'fields': ('features_text', 'features', 'is_premium', 'canva_url')
        }),
        ('Metadata', {
            'fields': ('date_added', 'last_modified', 'download_count')
        }),
    )

@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('template', 'user', 'rating', 'created_at')
    list_filter = ('rating', 'created_at')
    search_fields = ('template__title', 'user__username', 'comment')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(Favorite)
class FavoriteAdmin(admin.ModelAdmin):
    list_display = ('user', 'template', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__username', 'template__title')
    readonly_fields = ('created_at',)
