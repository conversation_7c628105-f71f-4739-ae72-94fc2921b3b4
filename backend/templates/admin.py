from django.contrib import admin
from .models import Category, Format, Tag, Template, TemplateImage, Review, Favorite

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'featured', 'template_count')
    prepopulated_fields = {'slug': ('name',)}
    search_fields = ('name', 'description')
    list_filter = ('featured',)

@admin.register(Format)
class FormatAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

@admin.register(Tag)
class TagAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug')
    prepopulated_fields = {'slug': ('name',)}
    search_fields = ('name',)

class TemplateImageInline(admin.TabularInline):
    model = TemplateImage
    extra = 1
    fields = ('image', 'order', 'alt_text')

@admin.register(Template)
class TemplateAdmin(admin.ModelAdmin):
    list_display = ('title', 'category', 'is_premium', 'date_added', 'download_count', 'rating', 'review_count')
    list_filter = ('category', 'is_premium', 'formats', 'tags')
    search_fields = ('title', 'description')
    prepopulated_fields = {'slug': ('title',)}
    readonly_fields = ('date_added', 'last_modified', 'download_count')
    filter_horizontal = ('formats', 'tags')
    inlines = [TemplateImageInline]
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'slug', 'category', 'description', 'thumbnail', 'template_file')
        }),
        ('Classifications', {
            'fields': ('formats', 'tags')
        }),
        ('Features', {
            'fields': ('features', 'is_premium', 'canva_url')
        }),
        ('Metadata', {
            'fields': ('date_added', 'last_modified', 'download_count')
        }),
    )

@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('template', 'user', 'rating', 'created_at')
    list_filter = ('rating', 'created_at')
    search_fields = ('template__title', 'user__username', 'comment')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(Favorite)
class FavoriteAdmin(admin.ModelAdmin):
    list_display = ('user', 'template', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__username', 'template__title')
    readonly_fields = ('created_at',)
