# Generated by Django 5.2.3 on 2025-06-30 16:21

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('templates', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='favorite',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorites', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='review',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='template',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='templates', to='templates.category'),
        ),
        migrations.AddField(
            model_name='template',
            name='formats',
            field=models.ManyToManyField(related_name='templates', to='templates.format'),
        ),
        migrations.AddField(
            model_name='template',
            name='tags',
            field=models.ManyToManyField(blank=True, related_name='templates', to='templates.tag'),
        ),
        migrations.AddField(
            model_name='review',
            name='template',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='templates.template'),
        ),
        migrations.AddField(
            model_name='favorite',
            name='template',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorited_by', to='templates.template'),
        ),
        migrations.AddField(
            model_name='templateimage',
            name='template',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='templates.template'),
        ),
        migrations.AlterUniqueTogether(
            name='review',
            unique_together={('template', 'user')},
        ),
        migrations.AlterUniqueTogether(
            name='favorite',
            unique_together={('user', 'template')},
        ),
    ]
