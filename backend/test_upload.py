import requests
import json
import os

# API endpoint
url = "http://127.0.0.1:8000/api/templates/"

# Authentication token
token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxMzg4NTQ0LCJpYXQiOjE3NTEzMDIxNDQsImp0aSI6Ijg1NmNiMmE4NjA4YjQ5NGVhZmY4ODE5YzYwZDQ1OTViIiwidXNlcl9pZCI6Mn0.WY_831KRbRFsnnLqvhnHccDXHn0v_OsYjUBd8zJXQK0"
headers = {
    "Authorization": f"Bearer {token}"
}

# Template data
data = {
    "title": "Test Template with File Upload",
    "slug": "test-template-with-file-upload",
    "description": "This is a test template to verify file upload functionality",
    "category": 1,  # Business category ID
    "formats": [1],  # PowerPoint format ID
    "tags": [1, 2, 3],  # Some tag IDs
    "features": json.dumps(["Feature 1", "Feature 2"]),
    "is_premium": False
}

# Files to upload
files = {
    "thumbnail": ("thumbnail.jpg", open("media/template_thumbnails/modern-business-pitch-deck-thumbnail.jpg", "rb"), "image/jpeg"),
    "template_file": ("test_template.txt", open("test_template.txt", "rb"), "text/plain")
}

# Make the request
response = requests.post(url, headers=headers, data=data, files=files)

# Print the response
print(f"Status Code: {response.status_code}")
print(f"Response: {response.text}")
