import Image from "next/image"
import { Star, Download } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"

interface PresentationCardProps {
  presentation: {
    id: number
    title: string
    category: string
    formats: string[]
    downloads: string
    rating: number
    tags: string[]
    thumbnail: string
    featured?: boolean
    isPremium?: boolean
    slug: string
  }
  viewMode?: "grid" | "list"
}

function StarRating({ rating }: { rating: number }) {
  return (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`h-3.5 w-3.5 ${star <= rating ? "fill-yellow-400 text-yellow-400" : "fill-gray-200 text-gray-200"}`}
        />
      ))}
      <span className="text-xs text-gray-500 ml-1.5 font-medium">{rating}</span>
    </div>
  )
}

function FormatBadges({ formats }: { formats: string[] }) {
  const formatColors = {
    PowerPoint: "bg-orange-100 text-orange-700",
    "Google Slides": "bg-blue-100 text-blue-700",
    Keynote: "bg-gray-100 text-gray-700",
    Canva: "bg-purple-100 text-purple-700",
  }

  return (
    <div className="flex flex-wrap gap-1">
      {formats.slice(0, 3).map((format) => (
        <span
          key={format}
          className={`text-[5px] font-medium px-0.5 py-0.5 rounded ${formatColors[format as keyof typeof formatColors] || "bg-gray-100 text-gray-700"}`}
        >
          {format}
        </span>
      ))}
    </div>
  )
}

export function PresentationCard({ presentation, viewMode = "grid" }: PresentationCardProps) {
  if (viewMode === "list") {
    return (
      <Link href={`/template/${presentation.slug}`}>
        <Card className="group cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5 border-0 shadow-sm bg-white">
          <CardContent className="p-0">
            <div className="flex">
              <div className="relative w-48 flex-shrink-0">
                <Image
                  src={presentation.thumbnail || "/placeholder.svg"}
                  alt={presentation.title}
                  width={192}
                  height={108}
                  className="w-full aspect-video object-cover rounded-l-lg transition-transform duration-300 group-hover:scale-105"
                />

                {/* Category badge */}
                <div className="absolute top-2 right-2">
                  <span className="bg-white/95 backdrop-blur-sm text-gray-700 text-xs font-medium px-2 py-0.5 rounded-full shadow-sm">
                    {presentation.category}
                  </span>
                </div>

                {/* Premium/Free badge */}
                <div className="absolute top-2 left-2">
                  <span className={`text-xs font-semibold px-2 py-0.5 rounded-full shadow-sm ${
                    presentation.isPremium
                      ? "bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900"
                      : "bg-gradient-to-r from-green-400 to-green-500 text-green-900"
                  }`}>
                    {presentation.isPremium ? "PREMIUM" : "FREE"}
                  </span>
                </div>
              </div>

              <div className="flex-1 p-5 flex flex-col justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900 text-base mb-3 group-hover:text-blue-600 transition-colors leading-relaxed">
                    {presentation.title}
                  </h3>
                  <div className="mb-3">
                    <FormatBadges formats={presentation.formats} />
                  </div>
                </div>

                <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                  <StarRating rating={presentation.rating} />
                  <div className="flex items-center gap-1.5 text-xs text-gray-500 font-medium">
                    <Download className="h-3.5 w-3.5" />
                    {presentation.downloads}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </Link>
    )
  }

  return (
    <Link href={`/template/${presentation.slug}`}>
      <Card className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border-0 shadow-sm bg-white">
        <CardContent className="p-0">
          <div className="relative overflow-hidden rounded-t-lg">
            <Image
              src={presentation.thumbnail || "/placeholder.svg"}
              alt={presentation.title}
              width={400}
              height={225}
              className="w-full aspect-video object-cover transition-transform duration-500 group-hover:scale-105"
            />
            {/* Gradient overlay for better text readability */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

            {/* Category badge */}
            <div className="absolute top-3 right-3">
              <span className="bg-white/95 backdrop-blur-sm text-gray-700 text-xs font-medium px-2.5 py-1 rounded-full shadow-sm">
                {presentation.category}
              </span>
            </div>

            {/* Premium/Free badge */}
            <div className="absolute top-3 left-3">
              <span className={`text-xs font-semibold px-2.5 py-1 rounded-full shadow-sm ${
                presentation.isPremium
                  ? "bg-gradient-to-r from-yellow-400 to-yellow-500 text-yellow-900"
                  : "bg-gradient-to-r from-green-400 to-green-500 text-green-900"
              }`}>
                {presentation.isPremium ? "PREMIUM" : "FREE"}
              </span>
            </div>
          </div>

          <div className="p-5">
            {/* Title */}
            <h3 className="font-semibold text-gray-900 text-base mb-3 line-clamp-2 leading-relaxed group-hover:text-blue-600 transition-colors duration-200">
              {presentation.title}
            </h3>

            {/* Format badges */}
            <div className="mb-4">
              <FormatBadges formats={presentation.formats} />
            </div>

            {/* Rating and downloads */}
            <div className="flex items-center justify-between pt-2 border-t border-gray-100">
              <StarRating rating={presentation.rating} />
              <div className="flex items-center gap-1.5 text-xs text-gray-500 font-medium">
                <Download className="h-3.5 w-3.5" />
                {presentation.downloads}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
