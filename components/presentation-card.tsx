import Image from "next/image"
import { Star, Download } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"

interface PresentationCardProps {
  presentation: {
    id: number
    title: string
    category: string
    formats: string[]
    downloads: string
    rating: number
    tags: string[]
    thumbnail: string
    featured?: boolean
    isPremium?: boolean
    slug: string
  }
  viewMode?: "grid" | "list"
}

function StarRating({ rating }: { rating: number }) {
  return (
    <div className="flex items-center gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={`h-4 w-4 ${star <= rating ? "fill-yellow-400 text-yellow-400" : "fill-gray-200 text-gray-200"}`}
        />
      ))}
      <span className="text-sm text-gray-600 ml-1">{rating}</span>
    </div>
  )
}

function FormatBadges({ formats }: { formats: string[] }) {
  const formatColors = {
    PowerPoint: "bg-orange-100 text-orange-800",
    "Google Slides": "bg-blue-100 text-blue-800",
    Keynote: "bg-gray-100 text-gray-800",
    Canva: "bg-purple-100 text-purple-800",
  }

  return (
    <div className="flex flex-wrap gap-1">
      {formats.slice(0, 3).map((format) => (
        <Badge
          key={format}
          variant="secondary"
          className={`text-[9px] px-1 py-0 ${formatColors[format as keyof typeof formatColors] || "bg-gray-100 text-gray-800"}`}
        >
          {format}
        </Badge>
      ))}
    </div>
  )
}

export function PresentationCard({ presentation, viewMode = "grid" }: PresentationCardProps) {
  if (viewMode === "list") {
    return (
      <Link href={`/template/${presentation.slug}`}>
        <Card className="group cursor-pointer transition-all duration-300 hover:shadow-lg">
          <CardContent className="p-0">
            <div className="flex">
              <div className="relative w-48 flex-shrink-0">
                <Image
                  src={presentation.thumbnail || "/placeholder.svg"}
                  alt={presentation.title}
                  width={192}
                  height={108}
                  className="w-full aspect-video object-cover rounded-l-lg transition-transform duration-300 group-hover:scale-105"
                />
                <Badge variant="secondary" className="absolute top-2 right-2 bg-white/90 text-gray-700 text-xs">
                  {presentation.category}
                </Badge>
                <div className="absolute top-2 left-2">
                  <Badge className={presentation.isPremium ? "bg-yellow-600 text-white" : "bg-green-600 text-white"}>
                    {presentation.isPremium ? "PREMIUM" : "FREE"}
                  </Badge>
                </div>
              </div>
              <div className="flex-1 p-4 flex flex-col justify-between">
                <div>
                  <h3 className="font-semibold text-lg mb-2 group-hover:text-blue-600 transition-colors">
                    {presentation.title}
                  </h3>
                  <div className="mb-3">
                    <FormatBadges formats={presentation.formats} />
                  </div>

                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <StarRating rating={presentation.rating} />
                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <Download className="h-4 w-4" />
                      {presentation.downloads}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </Link>
    )
  }

  return (
    <Link href={`/template/${presentation.slug}`}>
      <Card className="group cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105">
        <CardContent className="p-0">
          <div className="relative overflow-hidden rounded-t-lg">
            <Image
              src={presentation.thumbnail || "/placeholder.svg"}
              alt={presentation.title}
              width={400}
              height={225}
              className="w-full aspect-video object-cover transition-transform duration-300 group-hover:scale-110"
            />
            <Badge variant="secondary" className="absolute top-3 right-3 bg-white/90 text-gray-700">
              {presentation.category}
            </Badge>
            <div className="absolute top-3 left-3">
              <Badge className={presentation.isPremium ? "bg-yellow-600 text-white" : "bg-green-600 text-white"}>
                {presentation.isPremium ? "PREMIUM" : "FREE"}
              </Badge>
            </div>
          </div>
          <div className="p-6">
            <h3 className="font-semibold text-base mb-3 line-clamp-2 group-hover:text-blue-600 transition-colors">
              {presentation.title}
            </h3>
            <div className="mb-4">
              <FormatBadges formats={presentation.formats} />
            </div>
            <div className="flex items-center justify-between">
              <StarRating rating={presentation.rating} />
              <div className="flex items-center gap-1 text-sm text-gray-600">
                <Download className="h-4 w-4" />
                {presentation.downloads}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
