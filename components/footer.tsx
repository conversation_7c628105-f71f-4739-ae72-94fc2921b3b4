import Link from "next/link"
import { Download, Twitter, Facebook, Instagram, Linkedin } from "lucide-react"

const footerNavigation = {
  main: [
    { name: "About", href: "/about" },
    { name: "Browse Templates", href: "/browse" },
    { name: "Categories", href: "/categories" },
    { name: "Contact", href: "/contact" },
  ],
  legal: [
    { name: "Terms of Service", href: "/terms" },
    { name: "Privacy Policy", href: "/privacy" },
    { name: "License Agreement", href: "/license" },
    { name: "Refund Policy", href: "/refunds" },
  ],
  social: [
    {
      name: "Twitter",
      href: "#",
      icon: Twitter,
    },
    {
      name: "Facebook",
      href: "#",
      icon: Facebook,
    },
    {
      name: "Instagram",
      href: "#",
      icon: Instagram,
    },
    {
      name: "LinkedIn",
      href: "#",
      icon: Linkedin,
    },
  ],
}

export function Footer() {
  return (
    <footer className="bg-white border-t border-gray-200">
      <div className="mx-auto max-w-7xl px-6 py-12 md:flex md:items-center md:justify-between lg:px-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full">
          {/* Logo and description */}
          <div className="mb-8 md:mb-0">
            <Link href="/" className="flex items-center gap-2 mb-4">
              <Download className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">TemplateHub</span>
            </Link>
            <p className="text-sm text-gray-600 max-w-md">
              Professional presentation templates for PowerPoint, Google Slides, Keynote, and Canva. Trusted by
              thousands of professionals worldwide.
            </p>
          </div>

          {/* Navigation */}
          <div className="grid grid-cols-2 gap-8 md:gap-12">
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-4">Company</h3>
              <ul className="space-y-3">
                {footerNavigation.main.map((item) => (
                  <li key={item.name}>
                    <Link href={item.href} className="text-sm text-gray-600 hover:text-blue-600 transition-colors">
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-4">Legal</h3>
              <ul className="space-y-3">
                {footerNavigation.legal.map((item) => (
                  <li key={item.name}>
                    <Link href={item.href} className="text-sm text-gray-600 hover:text-blue-600 transition-colors">
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom section */}
      <div className="border-t border-gray-200">
        <div className="mx-auto max-w-7xl px-6 py-6 md:flex md:items-center md:justify-between lg:px-8">
          <div className="flex justify-center space-x-6 md:order-2">
            {footerNavigation.social.map((item) => (
              <Link key={item.name} href={item.href} className="text-gray-400 hover:text-blue-600 transition-colors">
                <span className="sr-only">{item.name}</span>
                <item.icon className="h-5 w-5" aria-hidden="true" />
              </Link>
            ))}
          </div>
          <p className="mt-8 text-center text-xs leading-5 text-gray-500 md:order-1 md:mt-0">
            &copy; 2024 TemplateHub. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  )
}
