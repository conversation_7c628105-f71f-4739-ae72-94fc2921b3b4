import Link from "next/link"
import { ArrowR<PERSON> } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import type { Category as MockCategory } from "@/lib/categories-data"
import type { Category as ApiCategory } from "@/lib/api"

// Create a unified category type that works with both API and mock data
type UnifiedCategory = {
  id: string | number
  name: string
  slug: string
  description: string
  icon: string
  templateCount: number
  color: string
  featured: boolean
}

interface CategoryCardProps {
  category: MockCategory | ApiCategory
  showTemplates?: boolean
}

export function CategoryCard({ category, showTemplates = true }: CategoryCardProps) {
  return (
    <Link href={`/browse?category=${category.name}`}>
      <Card className="group cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 h-full">
        <CardContent className="p-6 h-full flex flex-col">
          <div className="flex items-center gap-3 mb-4">
            <div className="text-3xl">{category.icon}</div>
            <div className="flex-1">
              <h3 className="font-semibold text-xl text-gray-900 group-hover:text-blue-600 transition-colors">
                {category.name}
              </h3>
              <Badge variant="outline" className={`mt-1 ${category.color}`}>
                {category.templateCount} templates
              </Badge>
            </div>
          </div>

          <p className="text-gray-600 text-sm leading-relaxed mb-4 flex-1">{category.description}</p>

          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 p-0 h-auto font-medium"
            >
              Browse Templates
              <ArrowRight className="ml-1 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
