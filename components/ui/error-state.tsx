import { AlertCircle } from "lucide-react"
import { Button } from "./button"

interface ErrorStateProps {
  message?: string
  retryAction?: () => void
  showRetry?: boolean
}

export function ErrorState({ 
  message = "Something went wrong. Please try again.", 
  retryAction = () => window.location.reload(),
  showRetry = true
}: ErrorStateProps) {
  return (
    <div className="text-center py-16">
      <div className="flex justify-center mb-4">
        <AlertCircle className="h-12 w-12 text-red-500" />
      </div>
      <p className="text-red-500 mb-4">{message}</p>
      {showRetry && (
        <Button 
          variant="default" 
          onClick={retryAction} 
          className="bg-blue-600 hover:bg-blue-700"
        >
          Retry
        </Button>
      )}
    </div>
  )
}
