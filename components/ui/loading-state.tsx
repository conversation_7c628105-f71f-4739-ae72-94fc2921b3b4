import { Loader2 } from "lucide-react"

interface LoadingStateProps {
  message?: string
  size?: "small" | "medium" | "large"
}

export function LoadingState({ 
  message = "Loading...", 
  size = "medium" 
}: LoadingStateProps) {
  const sizeClasses = {
    small: "h-6 w-6",
    medium: "h-10 w-10",
    large: "h-14 w-14"
  }

  return (
    <div className="flex flex-col items-center justify-center py-16">
      <Loader2 className={`${sizeClasses[size]} animate-spin text-blue-600 mb-4`} />
      <p className="text-gray-600">{message}</p>
    </div>
  )
}
