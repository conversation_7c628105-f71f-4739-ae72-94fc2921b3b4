import { Search } from "lucide-react"
import { But<PERSON> } from "./button"

interface EmptyStateProps {
  title?: string
  message?: string
  icon?: React.ReactNode
  actionLabel?: string
  actionFn?: () => void
}

export function EmptyState({
  title = "No results found",
  message = "Try adjusting your filters or search terms",
  icon = <Search className="h-12 w-12 mx-auto text-gray-400" />,
  actionLabel,
  actionFn
}: EmptyStateProps) {
  return (
    <div className="text-center py-12">
      <div className="text-gray-400 mb-4">
        {icon}
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-4">{message}</p>
      {actionLabel && actionFn && (
        <Button onClick={actionFn}>{actionLabel}</Button>
      )}
    </div>
  )
}
