# Project Structure

```
.
├── .gitignore
├── README.md
├── app
│   ├── about
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── auth
│   │   ├── forgot-password
│   │   │   ├── layout.tsx
│   │   │   └── page.tsx
│   │   ├── login
│   │   │   ├── layout.tsx
│   │   │   └── page.tsx
│   │   ├── register
│   │   │   ├── layout.tsx
│   │   │   └── page.tsx
│   │   ├── reset-password
│   │   │   ├── layout.tsx
│   │   │   ├── loading.tsx
│   │   │   └── page.tsx
│   │   └── verify-email
│   │       ├── layout.tsx
│   │       ├── loading.tsx
│   │       └── page.tsx
│   ├── browse
│   │   ├── layout.tsx
│   │   ├── loading.tsx
│   │   └── page.tsx
│   ├── categories
│   │   ├── layout.tsx
│   │   ├── loading.tsx
│   │   └── page.tsx
│   ├── contact
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── dashboard
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── globals.css
│   ├── layout.tsx
│   ├── license
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── page.tsx
│   ├── privacy
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── refunds
│   │   ├── layout.tsx
│   │   └── page.tsx
│   └── template
├── components
│   ├── auth
│   │   ├── forgot-password-form.tsx
│   │   ├── login-form.tsx
│   │   ├── register-form.tsx
│   │   └── reset-password-form.tsx
│   ├── icons.tsx
│   ├── main-nav.tsx
│   ├── page-header.tsx
│   ├── site-footer.tsx
│   └── ui
├── components.json
├── hooks
│   ├── use-debounce.ts
│   ├── use-is-client.ts
│   └── use-signin-redirect.ts
├── lib
│   ├── utils.ts
│   └── validators
├── next.config.mjs
├── package.json
├── pnpm-lock.yaml
├── postcss.config.mjs
├── public
│   └── next.svg
├── styles
│   └── globals.css
├── tailwind.config.ts
└── tsconfig.json
```
