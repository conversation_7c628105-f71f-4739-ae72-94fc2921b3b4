# Django Backend Integration for Next.js Slides Project

## Overview
This document outlines the plan for integrating a Django backend with the existing Next.js frontend application. The backend will provide RESTful API endpoints to support all frontend functionality, including authentication, content management, and media handling.

## Architecture
- **Frontend**: Next.js (existing)
- **Backend**: Django + Django REST Framework
- **Database**: PostgreSQL
- **Authentication**: JWT-based authentication
- **File Storage**: Local storage with option for cloud storage (AWS S3, etc.)

## Features

### 1. Authentication System
- User registration, login, logout
- Password reset functionality
- Email verification
- JWT token authentication
- Session management
- Optional: Social authentication

### 2. User Profiles
- Profile creation and management
- User preferences
- Activity history
- Profile image upload

### 3. Reviews System
- Create/read/update/delete reviews
- Rating system (star ratings)
- Review moderation for admins
- Sorting and filtering options

### 4. Favorites/Bookmarks
- Save favorite templates
- Organize favorites into collections
- Quick access to favorites
- Sync favorites across devices

### 5. Media Management
- Image uploads with validation
- File uploads (documents, presentations)
- Storage management
- Thumbnail generation
- Image optimization

### 6. Content Management
- Template categories and tags
- Search functionality with filters
- Featured content management
- Content recommendations

### 7. Admin Panel
- User management
- Content moderation
- Analytics dashboard
- System settings
- Permissions management

## Required Libraries

### Core & API
- **Django** - Web framework
- **Django REST Framework** - RESTful API toolkit
- **django-cors-headers** - Handle Cross-Origin Resource Sharing
- **django-filter** - Advanced filtering for APIs

### Authentication & Users
- **django-rest-framework-simplejwt** - JWT authentication
- **django-allauth** (optional) - Enhanced authentication including social auth

### File & Media Handling
- **Pillow** - Python Imaging Library for image processing
- **django-storages** - Abstract storage API with backends for S3, Azure, etc.
- **django-cleanup** - Automatically delete unused media files
- **django-imagekit** - Image processing for thumbnails and resizing

### Database & Performance
- **psycopg2-binary** - PostgreSQL adapter
- **django-redis** (optional) - Redis cache backend for performance
- **django-debug-toolbar** (dev only) - Debug performance issues

### Documentation & Testing
- **drf-spectacular** or **drf-yasg** - API documentation
- **pytest-django** - Testing framework
- **factory-boy** - Test data generation

### Utilities
- **python-dotenv** - Environment variable management
- **celery** (optional) - Task queue for handling background processes
- **django-taggit** - Tagging functionality for templates

## Implementation Plan

### Phase 1: Project Setup
- Create Django project structure
- Configure PostgreSQL database
- Set up Django REST Framework
- Configure CORS and security settings

### Phase 2: Authentication
- Implement JWT authentication
- Create user registration and login endpoints
- Set up password reset functionality
- Implement email verification

### Phase 3: Core Features
- Develop user profiles
- Implement reviews system
- Create favorites/bookmarks functionality
- Set up media uploads and management

### Phase 4: Frontend Integration
- Update Next.js API calls to use Django endpoints
- Modify authentication flows in frontend
- Test all features end-to-end

### Phase 5: Deployment
- Configure production settings
- Set up CI/CD pipeline
- Deploy Django backend
- Document API endpoints

## Getting Started (Draft)

```bash
# Create backend directory
mkdir backend
cd backend

# Set up virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install required packages
pip install django djangorestframework django-cors-headers psycopg2-binary python-dotenv

# Create Django project and app
django-admin startproject slides_backend .
python manage.py startapp api

# Install additional dependencies
pip install Pillow django-storages django-cleanup django-filter drf-spectacular django-rest-framework-simplejwt

# Create requirements.txt
pip freeze > requirements.txt
```

## API Endpoints (Draft)

### Authentication
- `POST /api/auth/register/` - Register new user
- `POST /api/auth/login/` - Login user
- `POST /api/auth/token/refresh/` - Refresh JWT token
- `POST /api/auth/password-reset/` - Request password reset
- `POST /api/auth/password-reset/confirm/` - Confirm password reset

### User Profiles
- `GET /api/users/me/` - Get current user profile
- `PATCH /api/users/me/` - Update current user profile
- `GET /api/users/{id}/` - Get user profile by ID

### Templates
- `GET /api/templates/` - List all templates
- `GET /api/templates/{id}/` - Get template details
- `GET /api/templates/categories/` - List template categories

### Reviews
- `GET /api/templates/{id}/reviews/` - Get reviews for a template
- `POST /api/templates/{id}/reviews/` - Create a review
- `PATCH /api/reviews/{id}/` - Update a review
- `DELETE /api/reviews/{id}/` - Delete a review

### Favorites
- `GET /api/favorites/` - List user's favorite templates
- `POST /api/favorites/` - Add template to favorites
- `DELETE /api/favorites/{id}/` - Remove template from favorites

## Next Steps
1. Set up the Django project structure
2. Configure PostgreSQL database
3. Implement authentication system
4. Create initial API endpoints
5. Test with the Next.js frontend
