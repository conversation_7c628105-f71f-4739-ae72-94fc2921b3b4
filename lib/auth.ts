// Authentication utilities for Django backend integration
const API_URL = 'http://127.0.0.1:8000/api';

export interface User {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
  profile_image?: string
  bio?: string
  date_joined: string
  is_active: boolean
}

export interface AuthTokens {
  access: string
  refresh: string
}

export interface AuthState {
  user: User | null
  loading: boolean
  error: string | null
}

// Token management utilities
export const tokenManager = {
  getAccessToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem('access_token')
  },

  getRefreshToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem('refresh_token')
  },

  setTokens(tokens: AuthTokens): void {
    if (typeof window === 'undefined') return
    localStorage.setItem('access_token', tokens.access)
    localStorage.setItem('refresh_token', tokens.refresh)
  },

  clearTokens(): void {
    if (typeof window === 'undefined') return
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user')
  },

  async refreshAccessToken(): Promise<string | null> {
    const refreshToken = this.getRefreshToken()
    if (!refreshToken) return null

    try {
      const response = await fetch(`${API_URL}/auth/token/refresh/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh: refreshToken }),
      })

      if (!response.ok) {
        this.clearTokens()
        return null
      }

      const data = await response.json()
      localStorage.setItem('access_token', data.access)
      return data.access
    } catch (error) {
      console.error('Token refresh failed:', error)
      this.clearTokens()
      return null
    }
  }
}

// API functions for authentication
export const authAPI = {
  async login(email: string, password: string): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      // Get JWT tokens
      const tokenResponse = await fetch(`${API_URL}/auth/token/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      if (!tokenResponse.ok) {
        const errorData = await tokenResponse.json()
        throw new Error(errorData.detail || 'Invalid credentials')
      }

      const tokens: AuthTokens = await tokenResponse.json()

      // Get user profile
      const userResponse = await fetch(`${API_URL}/auth/users/me/`, {
        headers: {
          'Authorization': `Bearer ${tokens.access}`,
          'Content-Type': 'application/json',
        },
      })

      if (!userResponse.ok) {
        throw new Error('Failed to fetch user profile')
      }

      const user: User = await userResponse.json()

      return { user, tokens }
    } catch (error) {
      console.error('Login error:', error)
      throw error
    }
  },

  async register(userData: {
    email: string
    password: string
    password_confirm: string
    first_name: string
    last_name: string
  }): Promise<User> {
    try {
      // Use email as username since that's our USERNAME_FIELD now
      const registrationData = {
        username: userData.email, // Set username to email for Django compatibility
        email: userData.email,
        password: userData.password,
        password_confirm: userData.password_confirm,
        first_name: userData.first_name,
        last_name: userData.last_name,
      }

      const response = await fetch(`${API_URL}/auth/users/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registrationData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        // Handle validation errors
        if (errorData.username) {
          throw new Error(`Username: ${errorData.username[0]}`)
        }
        if (errorData.email) {
          throw new Error(`Email: ${errorData.email[0]}`)
        }
        if (errorData.password) {
          throw new Error(`Password: ${errorData.password[0]}`)
        }
        throw new Error(errorData.detail || 'Registration failed')
      }

      const user: User = await response.json()
      return user
    } catch (error) {
      console.error('Registration error:', error)
      throw error
    }
  },

  async getCurrentUser(): Promise<User | null> {
    const token = tokenManager.getAccessToken()
    if (!token) return null

    try {
      const response = await fetch(`${API_URL}/auth/users/me/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })

      if (response.status === 401) {
        // Try to refresh token
        const newToken = await tokenManager.refreshAccessToken()
        if (newToken) {
          // Retry with new token
          const retryResponse = await fetch(`${API_URL}/auth/users/me/`, {
            headers: {
              'Authorization': `Bearer ${newToken}`,
              'Content-Type': 'application/json',
            },
          })

          if (retryResponse.ok) {
            return await retryResponse.json()
          }
        }

        // If refresh failed, clear tokens
        tokenManager.clearTokens()
        return null
      }

      if (!response.ok) {
        throw new Error('Failed to fetch user profile')
      }

      return await response.json()
    } catch (error) {
      console.error('Get current user error:', error)
      return null
    }
  },

  async updateProfile(data: Partial<User>): Promise<User> {
    const token = tokenManager.getAccessToken()
    if (!token) throw new Error('Not authenticated')

    try {
      const response = await fetch(`${API_URL}/auth/users/me/`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Failed to update profile')
      }

      return await response.json()
    } catch (error) {
      console.error('Update profile error:', error)
      throw error
    }
  },

  async changePassword(oldPassword: string, newPassword: string): Promise<void> {
    const token = tokenManager.getAccessToken()
    if (!token) throw new Error('Not authenticated')

    try {
      // First get current user ID
      const user = await this.getCurrentUser()
      if (!user) throw new Error('Not authenticated')

      const response = await fetch(`${API_URL}/auth/users/${user.id}/change_password/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          old_password: oldPassword,
          new_password: newPassword,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        if (errorData.old_password) {
          throw new Error(errorData.old_password[0])
        }
        throw new Error(errorData.detail || 'Failed to change password')
      }
    } catch (error) {
      console.error('Change password error:', error)
      throw error
    }
  },

  async logout(): Promise<void> {
    // Clear tokens from storage
    tokenManager.clearTokens()
    // Note: JWT tokens are stateless, so no server-side logout needed
    // In production, you might want to implement token blacklisting
  },

  // Note: These features would require additional backend implementation
  async forgotPassword(email: string): Promise<void> {
    // TODO: Implement password reset endpoint in Django
    throw new Error('Password reset not yet implemented')
  },

  async resetPassword(token: string, password: string): Promise<void> {
    // TODO: Implement password reset confirmation endpoint in Django
    throw new Error('Password reset not yet implemented')
  },

  async verifyEmail(token: string): Promise<void> {
    // TODO: Implement email verification endpoint in Django
    throw new Error('Email verification not yet implemented')
  },
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function validatePassword(password: string): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  if (password.length < 8) {
    errors.push("Password must be at least 8 characters long")
  }

  if (!/[A-Z]/.test(password)) {
    errors.push("Password must contain at least one uppercase letter")
  }

  if (!/[a-z]/.test(password)) {
    errors.push("Password must contain at least one lowercase letter")
  }

  if (!/\d/.test(password)) {
    errors.push("Password must contain at least one number")
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}
