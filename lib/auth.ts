// Mock authentication utilities - replace with your actual auth service
export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  emailVerified: boolean
  createdAt: string
  subscription?: "free" | "premium"
}

export interface AuthState {
  user: User | null
  loading: boolean
  error: string | null
}

// Mock API functions - replace with actual API calls
export const authAPI = {
  async login(email: string, password: string): Promise<User> {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    if (email === "<EMAIL>" && password === "password") {
      return {
        id: "1",
        email: "<EMAIL>",
        name: "Demo User",
        emailVerified: true,
        createdAt: "2024-01-01",
        subscription: "free",
      }
    }

    throw new Error("Invalid credentials")
  },

  async register(email: string, password: string, name: string): Promise<User> {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    return {
      id: "2",
      email,
      name,
      emailVerified: false,
      createdAt: new Date().toISOString(),
      subscription: "free",
    }
  },

  async forgotPassword(email: string): Promise<void> {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))
  },

  async resetPassword(token: string, password: string): Promise<void> {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))
  },

  async verifyEmail(token: string): Promise<void> {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))
  },

  async updateProfile(data: Partial<User>): Promise<User> {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    return {
      id: "1",
      email: "<EMAIL>",
      name: data.name || "Demo User",
      emailVerified: true,
      createdAt: "2024-01-01",
      subscription: "free",
      ...data,
    }
  },

  async logout(): Promise<void> {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 500))
  },
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function validatePassword(password: string): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  if (password.length < 8) {
    errors.push("Password must be at least 8 characters long")
  }

  if (!/[A-Z]/.test(password)) {
    errors.push("Password must contain at least one uppercase letter")
  }

  if (!/[a-z]/.test(password)) {
    errors.push("Password must contain at least one lowercase letter")
  }

  if (!/\d/.test(password)) {
    errors.push("Password must contain at least one number")
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}
