// API Service for fetching data from the backend
import { Template } from './templates-data';

const API_URL = 'http://127.0.0.1:8000/api';

// Category interface
export interface Category {
  id: number;
  slug: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  featured: boolean;
  templateCount: number;
}

// Interface for API responses
interface ApiResponse<T> {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results: T[];
}

// Convert backend template to frontend template format
const convertTemplate = (backendTemplate: any): Template => {
  return {
    id: backendTemplate.id,
    slug: backendTemplate.slug,
    title: backendTemplate.title,
    category: typeof backendTemplate.category === 'object' ? backendTemplate.category.name : backendTemplate.category,
    formats: backendTemplate.formats?.map((format: any) => typeof format === 'object' ? format.name : format) || [],
    tags: backendTemplate.tags?.map((tag: any) => typeof tag === 'object' ? tag.name : tag) || [],
    downloads: backendTemplate.download_count?.toString() || '0',
    rating: backendTemplate.rating || 0,
    reviewCount: backendTemplate.review_count || 0,
    thumbnail: backendTemplate.thumbnail || '/placeholder.svg',
    description: backendTemplate.description || '',
    features: backendTemplate.features || [],
    isPremium: backendTemplate.is_premium || false,
    dateAdded: backendTemplate.date_added,
    slides: backendTemplate.images?.map((image: any, index: number) => ({
      id: index + 1,
      thumbnail: image.image,
    })) || [],
    canvaUrl: backendTemplate.canva_url,
    template_file: backendTemplate.template_file,
  };
};

// Fetch all templates
export async function fetchAllTemplates(): Promise<Template[]> {
  try {
    const response = await fetch(`${API_URL}/templates/`);
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    const data = await response.json();

    // Handle both paginated response {results: [...]} and direct array [...]
    const templates = Array.isArray(data) ? data : data.results || [];
    return templates.map(convertTemplate);
  } catch (error) {
    console.error('Error fetching templates:', error);
    return [];
  }
}

// Fetch template by slug
export async function fetchTemplateBySlug(slug: string): Promise<Template | null> {
  try {
    const response = await fetch(`${API_URL}/templates/?slug=${slug}`);
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    const data = await response.json();

    // Handle both paginated response {results: [...]} and direct array [...]
    const templates = Array.isArray(data) ? data : data.results || [];
    if (templates.length === 0) {
      return null;
    }
    return convertTemplate(templates[0]);
  } catch (error) {
    console.error(`Error fetching template with slug ${slug}:`, error);
    return null;
  }
}

// Fetch related templates (same category, excluding current template)
export async function fetchRelatedTemplates(slug: string, category: string, limit = 4): Promise<Template[]> {
  try {
    const response = await fetch(`${API_URL}/templates/?category=${encodeURIComponent(category)}`);
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    const data = await response.json();

    // Handle both paginated response {results: [...]} and direct array [...]
    const templates = Array.isArray(data) ? data : data.results || [];
    return templates
      .filter((template: any) => template.slug !== slug)
      .slice(0, limit)
      .map(convertTemplate);
  } catch (error) {
    console.error('Error fetching related templates:', error);
    return [];
  }
}

// Fetch templates by category
export async function fetchTemplatesByCategory(category: string): Promise<Template[]> {
  try {
    const response = await fetch(`${API_URL}/templates/?category=${encodeURIComponent(category)}`);
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    const data = await response.json();

    // Handle both paginated response {results: [...]} and direct array [...]
    const templates = Array.isArray(data) ? data : data.results || [];
    return templates.map(convertTemplate);
  } catch (error) {
    console.error(`Error fetching templates for category ${category}:`, error);
    return [];
  }
}

// Fetch templates by tag
export async function fetchTemplatesByTag(tag: string): Promise<Template[]> {
  try {
    const response = await fetch(`${API_URL}/templates/?tags=${encodeURIComponent(tag)}`);
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    const data = await response.json();

    // Handle both paginated response {results: [...]} and direct array [...]
    const templates = Array.isArray(data) ? data : data.results || [];
    return templates.map(convertTemplate);
  } catch (error) {
    console.error(`Error fetching templates for tag ${tag}:`, error);
    return [];
  }
}

// Fetch template by ID
export async function fetchTemplateById(id: number): Promise<Template | null> {
  try {
    const response = await fetch(`${API_URL}/templates/${id}/`);
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    const data = await response.json();
    return convertTemplate(data);
  } catch (error) {
    console.error(`Error fetching template with ID ${id}:`, error);
    return null;
  }
}

// Download template file
export async function downloadTemplate(slug: string): Promise<{ success: boolean; error?: string; downloadUrl?: string }> {
  try {
    // First increment the download count
    const incrementResponse = await fetch(`${API_URL}/templates/${slug}/increment_downloads/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!incrementResponse.ok) {
      console.warn('Failed to increment download count, but continuing with download');
    }

    // Get the template to check if it has a file
    const template = await fetchTemplateBySlug(slug);
    if (!template) {
      return { success: false, error: 'Template not found' };
    }

    if (!template.template_file) {
      return { success: false, error: 'No downloadable file available for this template' };
    }

    // Return the download URL (the backend already provides the full URL)
    // template.template_file contains the full URL like "http://127.0.0.1:8000/media/template_files/test_template.txt"
    return { success: true, downloadUrl: template.template_file };

  } catch (error) {
    console.error('Error downloading template:', error);
    return { success: false, error: 'Failed to download template. Please try again.' };
  }
}

// Convert backend category to frontend category format
const convertCategory = (backendCategory: any): Category => {
  return {
    id: backendCategory.id,
    slug: backendCategory.slug,
    name: backendCategory.name,
    description: backendCategory.description,
    icon: backendCategory.icon || '📄', // Default icon if none provided
    color: backendCategory.color || 'bg-gray-100 text-gray-800 border-gray-200', // Default color
    featured: backendCategory.featured || false,
    templateCount: backendCategory.template_count || 0,
  };
};

// Fetch all categories
export async function fetchAllCategories(): Promise<Category[]> {
  try {
    const response = await fetch(`${API_URL}/categories/`);
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    const data = await response.json();

    // Handle both paginated response {results: [...]} and direct array [...]
    const categories = Array.isArray(data) ? data : data.results || [];
    return categories.map(convertCategory);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

// Fetch featured categories
export async function fetchFeaturedCategories(): Promise<Category[]> {
  try {
    const response = await fetch(`${API_URL}/categories/?featured=true`);
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    const data = await response.json();

    // Handle both paginated response {results: [...]} and direct array [...]
    const categories = Array.isArray(data) ? data : data.results || [];
    return categories.map(convertCategory);
  } catch (error) {
    console.error('Error fetching featured categories:', error);
    return [];
  }
}

// Fetch category by slug
export async function fetchCategoryBySlug(slug: string): Promise<Category | null> {
  try {
    const response = await fetch(`${API_URL}/categories/${slug}/`);
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    const data = await response.json();
    return convertCategory(data);
  } catch (error) {
    console.error(`Error fetching category with slug ${slug}:`, error);
    return null;
  }
}
