export interface Category {
  id: string
  name: string
  slug: string // Move slug after name
  description: string
  icon: string
  templateCount: number
  color: string
  featured: boolean
}

export const categoriesData: Category[] = [
  {
    id: "business",
    name: "Business",
    slug: "business",
    description:
      "Professional business presentations, pitch decks, and corporate templates for meetings and proposals.",
    icon: "💼",
    templateCount: 45,
    color: "bg-blue-100 text-blue-800 border-blue-200",
    featured: true,
  },
  {
    id: "marketing",
    name: "Marketing",
    slug: "marketing",
    description:
      "Marketing strategy presentations, campaign decks, and promotional templates for agencies and marketers.",
    icon: "📈",
    templateCount: 32,
    color: "bg-green-100 text-green-800 border-green-200",
    featured: true,
  },
  {
    id: "creative",
    name: "Creative",
    slug: "creative",
    description: "Portfolio showcases, creative briefs, and artistic presentation templates for designers and artists.",
    icon: "🎨",
    templateCount: 28,
    color: "bg-purple-100 text-purple-800 border-purple-200",
    featured: true,
  },
  {
    id: "corporate",
    name: "Corporate",
    slug: "corporate",
    description: "Annual reports, company overviews, and formal corporate presentation templates.",
    icon: "🏢",
    templateCount: 24,
    color: "bg-gray-100 text-gray-800 border-gray-200",
    featured: true,
  },
  {
    id: "finance",
    name: "Finance",
    slug: "finance",
    description:
      "Financial reports, investment presentations, and budget analysis templates for finance professionals.",
    icon: "💰",
    templateCount: 19,
    color: "bg-yellow-100 text-yellow-800 border-yellow-200",
    featured: false,
  },
  {
    id: "education",
    name: "Education",
    slug: "education",
    description: "Educational presentations, course materials, and academic templates for teachers and students.",
    icon: "📚",
    templateCount: 35,
    color: "bg-indigo-100 text-indigo-800 border-indigo-200",
    featured: false,
  },
  {
    id: "technology",
    name: "Technology",
    slug: "technology",
    description: "Tech presentations, product launches, and software development templates for tech companies.",
    icon: "💻",
    templateCount: 22,
    color: "bg-cyan-100 text-cyan-800 border-cyan-200",
    featured: false,
  },
  {
    id: "sales",
    name: "Sales",
    slug: "sales",
    description: "Sales presentations, proposal templates, and client pitch decks for sales professionals.",
    icon: "📊",
    templateCount: 18,
    color: "bg-orange-100 text-orange-800 border-orange-200",
    featured: false,
  },
  {
    id: "healthcare",
    name: "Healthcare",
    slug: "healthcare",
    description: "Medical presentations, healthcare reports, and clinical templates for healthcare professionals.",
    icon: "🏥",
    templateCount: 15,
    color: "bg-red-100 text-red-800 border-red-200",
    featured: false,
  },
  {
    id: "nonprofit",
    name: "Non-Profit",
    slug: "non-profit",
    description:
      "Fundraising presentations, impact reports, and community outreach templates for non-profit organizations.",
    icon: "🤝",
    templateCount: 12,
    color: "bg-pink-100 text-pink-800 border-pink-200",
    featured: false,
  },
  {
    id: "startup",
    name: "Startup",
    slug: "startup",
    description: "Investor pitch decks, startup presentations, and entrepreneurial templates for new businesses.",
    icon: "🚀",
    templateCount: 26,
    color: "bg-violet-100 text-violet-800 border-violet-200",
    featured: false,
  },
  {
    id: "consulting",
    name: "Consulting",
    slug: "consulting",
    description: "Consulting presentations, strategy frameworks, and client proposal templates for consultants.",
    icon: "📋",
    templateCount: 21,
    color: "bg-teal-100 text-teal-800 border-teal-200",
    featured: false,
  },
]

export function getCategoryById(id: string): Category | undefined {
  return categoriesData.find((category) => category.id === id)
}

export function getFeaturedCategories(): Category[] {
  return categoriesData.filter((category) => category.featured)
}

export function getAllCategories(): Category[] {
  return categoriesData
}

export function getCategoryBySlug(slug: string): Category | undefined {
  return categoriesData.find((category) => category.slug === slug)
}
